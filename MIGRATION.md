# Migration Guide: v1 → v2 🚀

This guide helps you migrate from ETL v1 to the simplified v2 architecture.

## 📊 Overview of Changes

| Aspect | v1 | v2 | Migration Strategy |
|--------|----|----|-------------------|
| **Transformer Types** | 12+ complex types | 5 focused types | Map old types to new ones |
| **Built-in Transformers** | 20+ transformer classes | Expression functions | Replace with `$function()` calls |
| **Configuration** | Monolithic schema | Type-specific schemas | Split configs by transform type |
| **CSV Handling** | Duplicated across files | Consolidated utility | Automatic - no changes needed |
| **Expression Evaluation** | Inconsistent | Unified evaluator | Expressions work better now |

## 🔄 Transformer Type Mapping

### v1 → v2 Transform Types

| v1 Type | v2 Type | Notes |
|---------|---------|-------|
| `simple` | `simple` | ✅ No change |
| `many-to-one` | `join` | ✅ Renamed for clarity |
| `one-to-many` | `split` | ✅ Renamed for clarity |
| `lookup` | `lookup` | ✅ No change |
| `aggregation` | `aggregation` | ✅ No change |
| `duckdb` | ❌ Removed | Use aggregation or external SQL |

### Configuration Updates

**v1 Many-to-One:**
```yaml
transform:
  type: "many-to-one"
  primarySource: "contacts"
  joins:
    - sourceAlias: "accounts"
      joinType: "left"
      on:
        AccountId: "Id"  # Old format
```

**v2 Join:**
```yaml
transform:
  type: "join"  # Renamed
  primarySource: "contacts"
  joins:
    - sourceAlias: "accounts"
      joinType: "left"
      on:  # New format - array of objects
        - primaryField: "AccountId"
          joinField: "Id"
```

**v1 One-to-Many:**
```yaml
transform:
  type: "one-to-many"
  outputTargets:
    - target: "hot_leads"
      condition: "record.Score > 80"  # Old format
```

**v2 Split:**
```yaml
transform:
  type: "split"  # Renamed
  outputTargets:
    - target: "hot_leads"
      condition: "$Score > 80"  # New expression format
```

## 🧹 Built-in Transformers → Expression Functions

### Text Transformers

**v1 Built-in:**
```yaml
transform:
  type: "simple"
  mappings: { "*": "*" }
  transformers:
    - name: "cleanText"
      options:
        fields: ["Name", "Description"]
    - name: "truncateText"
      options:
        fieldMappings:
          "Description": 100
```

**v2 Expressions:**
```yaml
transform:
  type: "simple"
  mappings:
    "*": "*"
    Name: "$cleanText(Name)"
    Description: "$truncateText(cleanText(Description), 100)"
```

### Date Transformers

**v1 Built-in:**
```yaml
transformers:
  - name: "dateFormat"
    options:
      fieldMappings:
        "CreatedDate": "YYYY-MM-DD"
        "ModifiedDate": "ISO"
```

**v2 Expressions:**
```yaml
mappings:
  CreatedDate: "$formatDate(CreatedDate, 'YYYY-MM-DD')"
  ModifiedDate: "$formatDate(ModifiedDate, 'ISO')"
```

### Number Transformers

**v1 Built-in:**
```yaml
transformers:
  - name: "numberFormat"
    options:
      fieldMappings:
        "Amount": "currency"
        "Rate": "percentage"
```

**v2 Expressions:**
```yaml
mappings:
  Amount: "$formatNumber(Amount, 'currency')"
  Rate: "$formatNumber(Rate, 'percentage')"
```

### Type Conversion

**v1 Built-in:**
```yaml
transformers:
  - name: "typeConversion"
    options:
      fieldMappings:
        "Score":
          type: "number"
        "IsActive":
          type: "boolean"
```

**v2 Expressions:**
```yaml
mappings:
  Score: "$Number(Score)"
  IsActive: "$Boolean(IsActive)"
```

### Data Cleansing

**v1 Built-in:**
```yaml
transformers:
  - name: "dataCleansing"
    options:
      operations:
        - fields: ["Name"]
          operation: "trim"
        - fields: ["Email"]
          operation: "lowercase"
```

**v2 Expressions:**
```yaml
mappings:
  Name: "$Name.trim()"
  Email: "$Email.toLowerCase()"
```

## 📋 Configuration Schema Updates

### Simple Job Config

**v1 Monolithic Schema:**
```yaml
name: "Account Sync"
source: { ... }
transform:
  type: "simple"
  mappings: { ... }
  # 20+ optional fields for different transform types
  joins: null  # Not used but defined
  outputTargets: null  # Not used but defined
  aggregation: null  # Not used but defined
target: { ... }
```

**v2 Type-Specific Schema:**
```yaml
name: "Account Sync"
source: { ... }
transform:
  type: "simple"
  mappings: { ... }
  filter: "..."  # Only fields relevant to simple transforms
target: { ... }
```

### Migration Config

**v1:**
```yaml
migration:
  name: "Data Migration"
  # ... other fields
flows:
  contact_migration:
    type: "many-to-one"  # In flow config
    transform:
      mappings: { ... }
      primarySource: "contacts"
```

**v2:**
```yaml
migration:
  name: "Data Migration"
  # ... other fields
flows:
  contact_migration:
    # No type in flow config
    transform:
      type: "join"  # Type moved to transform config
      mappings: { ... }
      primarySource: "contacts"
```

## 🗑️ Removed Features

### 1. Circular Relationship Orchestrator

**If you were using:**
```yaml
migration:
  circularRelationshipConfig:
    maxIterations: 10
    convergenceThreshold: 0.1
```

**Migration path:**
1. **Extract logic** into separate preprocessing step
2. **Use standard flows** with explicit dependencies
3. **Consider external tools** for complex relationship mapping

### 2. DuckDB Transformer

**If you were using:**
```yaml
transform:
  type: "duckdb"
  duckdb:
    sqlScript: "complex_aggregation.sql"
```

**Migration path:**
1. **Simple aggregations** → Use `aggregation` transformer
2. **Complex SQL** → Use external tools (dbt, Apache Spark)
3. **Multi-table joins** → Use `join` transformer

### 3. Complex Built-in Transformers

**Standardization, Regex, Complex Type Conversion:**

**Migration path:**
1. **Phone formatting** → `$formatNumber(Phone, 'phone')` expression
2. **Regex operations** → Native JavaScript regex in expressions
3. **Complex standardization** → Custom expression functions

## ⚠️ Breaking Changes

### 1. Join Configuration Format

**v1:**
```yaml
on:
  AccountId: "Id"  # Object format
```

**v2:**
```yaml
on:  # Array format
  - primaryField: "AccountId"
    joinField: "Id"
```

### 2. Expression Syntax

**v1 Conditions:**
```yaml
condition: "record.Score > 80"  # Old format
```

**v2 Expressions:**
```yaml
condition: "$Score > 80"  # New format
```

### 3. Transform Type Names

- `many-to-one` → `join`
- `one-to-many` → `split`

### 4. Pipeline Architecture

**v1:** Complex pipeline with built-in transformers
```yaml
transform:
  type: "simple"
  transformers:  # No longer exists
    - name: "cleanText"
    - name: "dateFormat"
```

**v2:** Single transformer with expressions
```yaml
transform:
  type: "simple"
  mappings:
    Name: "$cleanText(Name)"
    Date: "$formatDate(Date, 'ISO')"
```

## 🛠️ Migration Tools

### Automated Migration Script

Create a migration script to help convert configurations:

```typescript
// migration-helper.ts
import * as fs from 'fs';
import * as yaml from 'js-yaml';

function migrateConfig(v1ConfigPath: string): any {
    const v1Config = yaml.load(fs.readFileSync(v1ConfigPath, 'utf8'));
    
    // Convert transform types
    if (v1Config.transform?.type === 'many-to-one') {
        v1Config.transform.type = 'join';
    }
    if (v1Config.transform?.type === 'one-to-many') {
        v1Config.transform.type = 'split';
    }
    
    // Convert join format
    if (v1Config.transform?.joins) {
        v1Config.transform.joins.forEach(join => {
            if (typeof join.on === 'object' && !Array.isArray(join.on)) {
                join.on = Object.entries(join.on).map(([primaryField, joinField]) => ({
                    primaryField: joinField,
                    joinField: primaryField
                }));
            }
        });
    }
    
    // Remove transformers and convert to expressions
    if (v1Config.transform?.transformers) {
        const mappings = v1Config.transform.mappings || {};
        
        v1Config.transform.transformers.forEach(transformer => {
            if (transformer.name === 'cleanText') {
                transformer.options.fields.forEach(field => {
                    mappings[field] = `$cleanText(${field})`;
                });
            }
            // Add more transformer conversions...
        });
        
        v1Config.transform.mappings = mappings;
        delete v1Config.transform.transformers;
    }
    
    return v1Config;
}

// Usage
const migratedConfig = migrateConfig('./old-config.yml');
fs.writeFileSync('./new-config.yml', yaml.dump(migratedConfig));
```

### Expression Converter

Convert v1 built-in transformers to v2 expressions:

```typescript
const transformerToExpression = {
    cleanText: (field: string) => `$cleanText(${field})`,
    dateFormat: (field: string, format: string) => `$formatDate(${field}, '${format}')`,
    numberFormat: (field: string, format: string) => `$formatNumber(${field}, '${format}')`,
    truncateText: (field: string, length: number) => `$truncateText(${field}, ${length})`
};
```

## ✅ Migration Checklist

### Pre-Migration
- [ ] **Backup v1 configurations** and test data
- [ ] **Review removed features** - plan alternatives
- [ ] **Update dependencies** to v2 packages
- [ ] **Test with small datasets** first

### Configuration Migration
- [ ] **Update transform types** (`many-to-one` → `join`, `one-to-many` → `split`)
- [ ] **Convert join configurations** (object → array format)
- [ ] **Replace built-in transformers** with expression functions
- [ ] **Update expression syntax** (`record.field` → `$field`)
- [ ] **Remove unused schema fields**

### Testing
- [ ] **Validate configs** with `etl validate`
- [ ] **Test connections** with `etl test-connections`
- [ ] **Run with limits** (`--limit 100`) for testing
- [ ] **Compare output** between v1 and v2
- [ ] **Check debug CSVs** for data accuracy

### Post-Migration
- [ ] **Update documentation** and runbooks
- [ ] **Train team** on new expression syntax
- [ ] **Monitor performance** (should be better!)
- [ ] **Celebrate** the cleaner codebase! 🎉

## 🆘 Migration Support

### Common Issues

**"Transform type not found"**
```
❌ Error: Unsupported transformer type: many-to-one
✅ Fix: Change to "join"
```

**"Built-in transformer missing"**
```
❌ Error: Unknown built-in transformer: cleanText
✅ Fix: Use $cleanText(field) in mappings
```

**"Join configuration invalid"**
```
❌ v1: on: { AccountId: "Id" }
✅ v2: on: [{ primaryField: "AccountId", joinField: "Id" }]
```

### Getting Help

1. **Check examples** in `/examples` directory
2. **Use debug mode** for detailed error messages
3. **Compare with working v2 configs**
4. **Open GitHub issues** with sanitized configs

---

## 🎯 Summary

**ETL v2 migration** = **Less complexity, same power**

The migration removes 900+ lines of over-engineered code while keeping all the functionality you actually use. Most migrations involve:

1. **Renaming transform types** (2 minutes)
2. **Converting built-in transformers** to expressions (10 minutes)  
3. **Updating join configurations** (5 minutes)
4. **Testing** (as long as needed!)

**Result:** Cleaner configs, better performance, easier maintenance! 🚀
