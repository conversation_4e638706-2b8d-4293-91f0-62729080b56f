# Salesforce ETL Tool v2 🚀

A simplified, streamlined ETL tool for Salesforce data migration and synchronization. Version 2 removes complexity while maintaining powerful functionality.

## ✨ What's New in v2

### 🧹 **Simplified Architecture**
- **Removed 900+ lines** of unused/over-engineered code
- **Consolidated CSV utilities** - no more code duplication
- **Unified expression evaluator** - consistent across all transformers
- **Type-specific configurations** - clearer, more maintainable configs

### 🗑️ **Removed Complexity**
- ❌ Over-engineered built-in transformers (replaced with expression functions)
- ❌ Circular relationship orchestrator (specialized use case)
- ❌ Complex inheritance features rarely used
- ❌ DuckDB transformer (overkill for most ETL tasks)
- ❌ Unused models and interfaces

### ✅ **Kept What Matters**
- ✅ **5 Core Transformers**: Simple, Join, Split, Lookup, Aggregation
- ✅ **REST & Bulk API 2.0** loaders with full error handling
- ✅ **Cross-flow data linking** for complex migrations
- ✅ **Built-in expression functions**: `cleanText()`, `formatDate()`, `formatNumber()`
- ✅ **Comprehensive debugging** with CSV export options

## 🏗️ Architecture

```
src/
├── cli/                 # Command line interface
├── config/              # Configuration management (simplified)
├── core/                # Core interfaces and orchestrator  
├── extractors/          # Data extraction (API, CSV, Flow-output)
├── loaders/             # Data loading (REST, Bulk2, CSV-export)
├── services/            # Connection management
├── transformers/        # Data transformation (5 core types)
└── utils/               # Consolidated utilities
```

## 🚀 Quick Start

### Installation

```bash
npm install salesforce-etl-v2
# or
yarn add salesforce-etl-v2
```

### CLI Usage

```bash
# Run a simple job
etl run examples/simple-account-sync.yml

# Run with record limit for testing
etl run examples/join-migration.yml --limit 100

# Validate configuration
etl validate examples/split-migration.yml

# Test connections
etl test-connections examples/aggregation-migration.yml

# List flows in a migration
etl list-flows examples/join-migration.yml

# Show tool info
etl info
```

### Programmatic Usage

```typescript
import { createEtlOrchestrator, runEtlJob } from 'salesforce-etl-v2';

// Simple execution
await runEtlJob('./config/my-etl.yml', {
    logLevel: 'info',
    recordLimit: 1000
});

// Advanced usage
const orchestrator = createEtlOrchestrator('debug');
const result = await orchestrator.executeFromFile('./config/migration.yml');

if (result.success) {
    console.log(`Processed ${result.recordsProcessed} records`);
} else {
    console.error('ETL failed:', result.errors);
}
```

## 📋 Configuration

### Simple Job Configuration

```yaml
name: "Account Sync"

connections:
  salesforce:
    sfdxAlias: "myorg"

source:
  type: "csv"
  filePath: "./data/accounts.csv"
  connection: "salesforce"

transform:
  type: "simple"
  mappings:
    "*": "*"  # Copy all fields
    Name: "$cleanText(Name)"
    Phone: "$formatNumber(Phone, 'phone')"
    CreatedDate: "$formatDate(new Date(), 'ISO')"
  filter: "$Name && Name.length > 0"

target:
  name: "accounts"
  object: "Account"
  connection: "salesforce"
  operation: "upsert"
  loadStrategy: "bulk2"
  externalId: "External_Id__c"
```

### Migration Configuration

```yaml
migration:
  name: "Contact Migration"
  version: "1.0.0"
  migrationOrder: ["extract_contacts", "transform_contacts"]
  continueOnError: false

connections:
  source: { sfdxAlias: "source-org" }
  target: { sfdxAlias: "target-org" }

dataSources:
  contacts:
    type: "api"
    connection: "source"
    object: "Contact"
    fields: ["Id", "Name", "Email"]

flows:
  extract_contacts:
    sources: [{ name: "contacts" }]
    transform:
      type: "simple"
      mappings: { "*": "*" }
    targets:
      - name: "contacts_csv"
        loadStrategy: "csv-export"
        filePath: "./output/contacts.csv"
```

## 🔧 Transformer Types

### 1. Simple Transformer
Field mapping with expression support:

```yaml
transform:
  type: "simple"
  mappings:
    Name: "$cleanText(First_Name + ' ' + Last_Name)"
    Email: "$Email.toLowerCase()"
    Phone: "$formatNumber(Phone, 'phone')"
  filter: "$Email && Email.includes('@')"
```

### 2. Join Transformer
Join multiple data sources:

```yaml
transform:
  type: "join"
  primarySource: "contacts"
  joins:
    - sourceAlias: "accounts"
      joinType: "left"
      on:
        - primaryField: "AccountId"
          joinField: "Id"
  mappings:
    ContactName: "contacts_Name"
    AccountName: "accounts_Name"
```

### 3. Split Transformer
Split records to multiple targets:

```yaml
transform:
  type: "split"
  outputTargets:
    - target: "hot_leads"
      condition: "$Score > 80"
      mappings: { "*": "*", Status: "'Hot'" }
    - target: "cold_leads"
      condition: "$Score <= 80"
      mappings: { "*": "*", Status: "'Cold'" }
```

### 4. Lookup Transformer
Enrich data with lookups:

```yaml
transform:
  type: "lookup"
  conditions:
    - type: "equals"
      sourceField: "Territory"
      lookupField: "Name"
    - type: "dateRange"
      sourceField: "StartDate"
      lookupStartField: "EffectiveDate"
      lookupEndField: "ExpirationDate"
  resultField: "TerritoryId"
  mappings: { "*": "*" }
```

### 5. Aggregation Transformer
SQL-like GROUP BY operations:

```yaml
transform:
  type: "aggregation"
  groupBy: ["OwnerId", "StageName"]
  aggregates:
    - field: "Amount"
      operation: "sum"
      targetField: "TotalRevenue"
    - field: "Id"
      operation: "count"
      targetField: "OpportunityCount"
  mappings:
    ReportDate: "$formatDate(new Date(), 'YYYY-MM-DD')"
```

## 🎯 Built-in Expression Functions

Use these in any mapping expression:

```yaml
mappings:
  # Text cleaning
  CleanName: "$cleanText(Name)"  # Removes extra spaces, normalizes
  
  # Date formatting  
  FormattedDate: "$formatDate(CreatedDate, 'YYYY-MM-DD')"
  IsoDate: "$formatDate(new Date(), 'ISO')"
  
  # Number formatting
  FormattedAmount: "$formatNumber(Amount, 'currency')"  # $1,234.56
  Percentage: "$formatNumber(Rate, 'percentage')"        # 12.34%
  Rounded: "$formatNumber(Value, 'decimal2')"           # 123.45
  
  # Text manipulation
  ShortText: "$truncateText(Description, 100)"
  
  # Conditional logic
  Status: "$Amount > 1000 ? 'High Value' : 'Standard'"
  
  # Built-in JavaScript functions
  UpperName: "$Name.toUpperCase()"
  EmailDomain: "$Email.split('@')[1]"
```

## 🔌 Data Sources & Targets

### Source Types
- **`api`**: Salesforce SOQL queries
- **`csv`**: Local CSV files  
- **`flow-output`**: Data from previous flows

### Load Strategies
- **`rest`**: Salesforce REST API (< 10k records)
- **`bulk2`**: Bulk API 2.0 (> 10k records) 
- **`csv-export`**: Export to CSV files

### Connection Types
- **SFDX**: `{ sfdxAlias: "myorg" }`
- **OAuth**: `{ accessToken: "xxx", instanceUrl: "xxx" }`
- **Username/Password**: `{ username: "xxx", password: "xxx", securityToken: "xxx" }`

## 🐛 Debugging

Enable debug options to save intermediate CSV files:

```yaml
debug:
  saveExtractorCsv: true   # Raw extracted data
  saveTransformerCsv: true # Transformed data

# Creates files in ./staging/ directory:
# - {job}_extracted_raw.csv
# - {job}_simple_transformed.csv  
# - {job}_join_transformed.csv
```

## 📊 Error Handling & Results

The tool provides comprehensive error handling:

```typescript
const result = await orchestrator.execute(config);

if (result.success) {
    console.log(`✅ Success: ${result.recordsProcessed} records`);
} else {
    console.log(`❌ Failed: ${result.errors.length} errors`);
    result.errors.forEach(error => {
        console.log(`- ${error.message}`);
    });
}

// For migrations
if ('flowResults' in result) {
    result.flowResults.forEach(flow => {
        console.log(`${flow.flowName}: ${flow.recordsProcessed} records`);
    });
}
```

## 🔄 Migration Features

- **Flow Dependencies**: Automatic execution ordering
- **Cross-flow Data**: Use output from one flow as input to another
- **Continue on Error**: Optional failure handling
- **Global Variables**: Share data across flows

```yaml
migration:
  continueOnError: true  # Keep going if a flow fails
  globalVariables:
    migrationDate: "2024-01-01"
    batchSize: 1000

# Use in expressions:
mappings:
  ProcessedDate: "$globalVariables.migrationDate"
```

## 🏆 Performance & Limits

### Salesforce Limits
- **REST API**: 1,000 records/request, 100,000 records/day
- **Bulk API 2.0**: 50,000 records/batch, 10,000 batches/day

### Recommendations
- Use **Bulk API 2.0** for > 10,000 records
- Set appropriate `batchSize` (500-2000 for Bulk, 200 for REST)
- Use `recordLimit` for testing large datasets
- Enable debug CSV files for troubleshooting

## 📚 Examples

Check the `/examples` directory for:
- `simple-account-sync.yml` - Basic CSV to Salesforce sync
- `join-migration.yml` - Multi-source join example
- `split-migration.yml` - Record splitting example  
- `aggregation-migration.yml` - Sales reporting aggregation

## 🆚 v1 vs v2 Comparison

| Feature | v1 | v2 |
|---------|----|----|
| **Lines of Code** | ~4,500 | ~3,600 (-20%) |
| **Transformer Types** | 12+ complex | 5 focused |
| **Built-in Functions** | 20+ scattered | 5 essential |
| **Configuration** | Monolithic | Type-specific |
| **CSV Handling** | Duplicated 4x | Consolidated |
| **Expression Eval** | Duplicated 3x | Unified |
| **Circular Relationships** | 400+ lines | Removed |
| **DuckDB Support** | Complex | Removed |
| **Maintainability** | Difficult | Simple |

## 🏃‍♂️ Migration from v1

### Configuration Changes

**v1 Transform Config:**
```yaml
transform:
  type: "many-to-one"  # ❌ Old name
  # 20+ optional fields
```

**v2 Transform Config:**
```yaml
transform:
  type: "join"  # ✅ Clear name
  # Type-specific fields only
```

### Built-in Transformers → Expression Functions

**v1 Built-in Transformers:**
```yaml
transformers:
  - name: "cleanText"
    options: { fields: ["Name"] }
  - name: "dateFormat"  
    options: { fieldMappings: { "CreatedDate": "YYYY-MM-DD" } }
```

**v2 Expression Functions:**
```yaml
mappings:
  Name: "$cleanText(Name)"
  CreatedDate: "$formatDate(CreatedDate, 'YYYY-MM-DD')"
```

### Removed Features

If you were using these v1 features:

- **Circular Relationships**: Move complex logic to separate preprocessing step
- **DuckDB Transformer**: Use aggregation transformer or external SQL tools
- **Complex Built-ins**: Replace with expression functions
- **Multiple Transform Types**: Consolidate to 5 core types

## 🐛 Troubleshooting

### Common Issues

**Connection Errors:**
```bash
# Test connections first
etl test-connections config.yml

# Check SFDX auth
sfdx force:org:list
```

**Transform Errors:**
```bash
# Enable debug mode
etl run config.yml --log-level debug

# Save intermediate CSVs
# Set debug.saveTransformerCsv: true in config
```

**Performance Issues:**
```yaml
# Optimize batch sizes
source:
  batchSize: 1000  # Extraction batch size
target:
  batchSize: 2000  # Load batch size
  loadStrategy: "bulk2"  # Use Bulk API for large datasets
```

### Debug Output

Enable debugging to save intermediate files:
```
./staging/
├── {job}_extracted_raw.csv      # Raw data from source
├── {job}_simple_transformed.csv  # After transformation  
└── job_results/
    └── {bulkJobId}/
        ├── successful_records.csv
        ├── failed_records.csv
        └── unprocessed_records.csv
```

## 🤝 Contributing

1. **Found a bug?** Open an issue with:
   - Configuration file (sanitized)
   - Error message and logs
   - Expected vs actual behavior

2. **Feature request?** Consider:
   - Is it needed by most users?
   - Can it be done with expressions?
   - Does it fit the simplified architecture?

3. **Pull requests welcome!** 
   - Keep it simple and focused
   - Add tests for new features
   - Update documentation

## 📄 License

MIT License - feel free to use in your projects!

---

## 🎯 **TL;DR**

ETL v2 = **Same Power, Less Complexity**

- ✅ **900+ lines removed** 
- ✅ **5 focused transformers** instead of 12+ scattered ones
- ✅ **Built-in functions** replace over-engineered transformers
- ✅ **Type-specific configs** replace monolithic schemas
- ✅ **Consolidated utilities** eliminate code duplication

**Perfect for:** Salesforce data migrations, ETL jobs, data synchronization

**Not for:** Complex data warehousing (use dedicated tools like Talend/Informatica)

Ready to migrate your data without the complexity? Give ETL v2 a try! 🚀
