# Sales Aggregation Report Example
migration:
  name: "Monthly Sales Aggregation"
  version: "1.0.0"
  migrationOrder:
    - "aggregate_sales"

connections:
  salesforce:
    sfdxAlias: "myorg"

dataSources:
  opportunities:
    type: "api"
    connection: "salesforce"
    object: "Opportunity"
    fields: ["Id", "Amount", "StageName", "CloseDate", "AccountId", "OwnerId"]
    whereClause: "StageName = 'Closed Won' AND CloseDate = THIS_YEAR"

flows:
  aggregate_sales:
    name: "Aggregate Sales by Month and Owner"
    sources:
      - name: "opportunities"

    transform:
      type: "aggregation"
      groupBy: 
        - "OwnerId"
        - "$formatDate(CloseDate, 'YYYY-MM')"  # Group by year-month
      aggregates:
        - field: "Amount"
          operation: "sum"
          targetField: "TotalRevenue"
        - field: "Id"
          operation: "count"
          targetField: "OpportunityCount"
        - field: "Amount"
          operation: "avg"
          targetField: "AverageOpportunityValue"
        - field: "Amount"
          operation: "max"
          targetField: "LargestOpportunity"
      mappings:
        "*": "*"  # Keep all aggregated fields
        SalesMonth: "$formatDate(CloseDate, 'YYYY-MM')"
        ReportDate: "$formatDate(new Date(), 'YYYY-MM-DD')"
        TotalRevenueFormatted: "$formatNumber(TotalRevenue, 'currency')"
      filter: "$TotalRevenue > 0"  # Only include months with revenue

    targets:
      - name: "sales_summary"
        object: "Monthly_Sales_Summary__c"
        connection: "salesforce"
        operation: "upsert"
        loadStrategy: "bulk2"
        externalId: "Month_Owner_Key__c"
