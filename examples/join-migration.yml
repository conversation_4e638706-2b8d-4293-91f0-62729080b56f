# Contact and Account Join Example
migration:
  name: "Contact Account Join <PERSON>"
  version: "1.0.0"
  migrationOrder:
    - "join_contacts_accounts"
  globalVariables:
    migrationDate: "2024-01-01"
    batchSize: 1000

# Connection configurations
connections:
  source_sf:
    sfdxAlias: "source-org"
    apiVersion: "57.0"
  target_sf:
    sfdxAlias: "target-org"
    apiVersion: "57.0"

# Data sources
dataSources:
  contacts_source:
    type: "api"
    connection: "source_sf"
    object: "Contact"
    fields: ["Id", "FirstName", "LastName", "Email", "AccountId", "CreatedDate"]
    whereClause: "CreatedDate >= 2023-01-01T00:00:00Z"
    batchSize: 500

  accounts_source:
    type: "api"
    connection: "source_sf"
    object: "Account"
    fields: ["Id", "Name", "Type", "Industry", "BillingCity"]
    whereClause: "CreatedDate >= 2023-01-01T00:00:00Z"
    batchSize: 500

# Flow definitions
flows:
  join_contacts_accounts:
    name: "Join Contacts with Account Data"
    sources:
      - name: "contacts_source"
        alias: "contacts"
      - name: "accounts_source"
        alias: "accounts"

    transform:
      type: "join"
      primarySource: "contacts"
      joins:
        - sourceAlias: "accounts"
          joinType: "left"
          on:
            - primaryField: "AccountId"
              joinField: "Id"
      mappings:
        "*": "*"  # Copy all fields
        ContactId: "contacts_Id"
        ContactName: "$contacts_FirstName + ' ' + contacts_LastName"
        ContactEmail: "contacts_Email"
        AccountName: "accounts_Name"
        AccountType: "accounts_Type"
        AccountIndustry: "accounts_Industry"
        AccountCity: "accounts_BillingCity"
        FullName: "$cleanText(contacts_FirstName + ' ' + contacts_LastName)"
        ProcessedDate: "$formatDate(new Date(), 'YYYY-MM-DD')"
      filter: "$contacts_Email && accounts_Name"  # Only records with email and account name

    targets:
      - name: "enriched_contacts"
        object: "Contact__c"
        connection: "target_sf"
        operation: "insert"
        loadStrategy: "bulk2"
        batchSize: 1000

    debug:
      saveTransformerCsv: true
