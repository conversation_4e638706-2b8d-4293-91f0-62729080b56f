# Simple Account Sync Example
name: "Account Sync from CSV"

# Connection configuration
connections:
  salesforce:
    sfdxAlias: "myorg"
    apiVersion: "57.0"

# Source: CSV file with account data
source:
  type: "csv"
  filePath: "./data/accounts.csv"
  batchSize: 500
  connection: "salesforce"

# Transform: Simple field mapping with built-in functions
transform:
  type: "simple"
  mappings:
    "*": "*"  # Copy all fields
    Name: "$cleanText(Name)"  # Clean up account names
    BillingStreet: "Street"
    BillingCity: "City"
    BillingState: "State"
    BillingPostalCode: "PostalCode"
    Phone: "$formatNumber(Phone, 'phone')"
    Website: "$Website.toLowerCase()"
    CreatedDate: "$formatDate(new Date(), 'ISO')"
  filter: "$Name && Name.length > 0"  # Only process accounts with names

# Target: Upsert to Salesforce using external ID
target:
  name: "accounts"
  object: "Account"
  connection: "salesforce"
  operation: "upsert"
  loadStrategy: "bulk2"
  externalId: "External_Id__c"
  batchSize: 1000

# Debug options
debug:
  saveExtractorCsv: true
  saveTransformerCsv: true
