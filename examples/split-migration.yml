# Opportunity Split Example
migration:
  name: "Opportunity Split by Stage"
  version: "1.0.0"
  migrationOrder:
    - "split_opportunities"

connections:
  salesforce:
    sfdxAlias: "myorg"

dataSources:
  opportunities:
    type: "api"
    connection: "salesforce"
    object: "Opportunity"
    fields: ["Id", "Name", "StageName", "Amount", "CloseDate", "AccountId"]
    whereClause: "CloseDate >= THIS_YEAR"

flows:
  split_opportunities:
    name: "Split Opportunities by Stage"
    sources:
      - name: "opportunities"

    transform:
      type: "split"
      outputTargets:
        - target: "won_opportunities"
          condition: "$StageName === 'Closed Won'"
          mappings:
            "*": "*"
            Status: "'Won'"
            ProcessedDate: "$formatDate(new Date(), 'YYYY-MM-DD')"
            
        - target: "lost_opportunities"
          condition: "$StageName === 'Closed Lost'"
          mappings:
            "*": "*"
            Status: "'Lost'"
            ProcessedDate: "$formatDate(new Date(), 'YYYY-MM-DD')"
            
        - target: "open_opportunities"
          condition: "$!StageName.includes('Closed')"
          mappings:
            "*": "*"
            Status: "'Open'"
            ProcessedDate: "$formatDate(new Date(), 'YYYY-MM-DD')"

    targets:
      - name: "won_opportunities"
        object: "Won_Opportunity__c"
        connection: "salesforce"
        operation: "insert"
        loadStrategy: "bulk2"
        
      - name: "lost_opportunities"
        object: "Lost_Opportunity__c"
        connection: "salesforce"
        operation: "insert"
        loadStrategy: "bulk2"
        
      - name: "open_opportunities"
        object: "Open_Opportunity__c"
        connection: "salesforce"
        operation: "insert"
        loadStrategy: "bulk2"
