{"name": "salesforce-etl-v2", "version": "2.0.0", "description": "Simplified Salesforce ETL Tool with consolidated architecture", "main": "dist/index.js", "bin": {"etl": "dist/cli/index.js"}, "scripts": {"build": "tsc", "start": "node dist/cli/index.js", "dev": "ts-node src/cli/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["salesforce", "etl", "data-migration", "bulk-api", "jsforce"], "author": "Your Name", "license": "MIT", "dependencies": {"commander": "^9.4.1", "csv-parser": "^3.0.0", "csv-writer": "^1.6.0", "dotenv": "^16.0.3", "js-yaml": "^4.1.0", "jsforce": "^3.8.2", "winston": "^3.8.2"}, "devDependencies": {"@types/glob": "^8.1.0", "@types/js-yaml": "^4.0.5", "@types/node": "^18.19.110", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "jest": "^29.3.1", "rimraf": "^3.0.2", "ts-jest": "^29.0.3", "ts-node": "^10.9.1", "typescript": "^4.9.4"}, "files": ["dist/**/*"], "engines": {"node": ">=14.0.0"}}