#!/usr/bin/env node

import * as commander from "commander";
import { createEtlOrchestrator, validateConfig } from "../index";
import {
    createEnhancedEtlOrchestrator,
    runStreamingTests,
} from "../improvements";
import * as dotenv from "dotenv";

// Load environment variables
dotenv.config();

const program = new commander.Command();

program
    .version("2.0.0")
    .description("Salesforce ETL Tool v2 - Simplified Architecture");

// Run command
program
    .command("run <configPath>")
    .description("Run an ETL job or migration")
    .option(
        "-l, --log-level <level>",
        "Set log level (debug, info, warn, error)",
        "info"
    )
    .option(
        "--limit <number>",
        "Limit number of records (for testing)",
        parseInt
    )
    .action(async (configPath, options) => {
        console.log(`🔧 Debug: configPath received: "${configPath}"`);
        console.log(`🔧 Debug: current working directory: "${process.cwd()}"`);

        try {
            const orchestrator = createEtlOrchestrator(options.logLevel);
            const result = await orchestrator.executeFromFile(
                configPath,
                options.limit
            );

            if (result.success) {
                console.log("✅ ETL job completed successfully");
                if ("migrationName" in result) {
                    console.log(`📊 Migration: ${result.migrationName}`);
                    console.log(
                        `📈 Total records: ${result.totalRecordsProcessed}`
                    );
                    console.log(`⏱️  Duration: ${result.totalDuration}ms`);
                    console.log(
                        `🔄 Flows completed: ${result.flowResults.length}`
                    );
                } else {
                    console.log(`📊 Flow: ${result.flowName}`);
                    console.log(
                        `📈 Records processed: ${result.recordsProcessed}`
                    );
                    console.log(`⏱️  Duration: ${result.duration}ms`);
                }
                process.exit(0);
            } else {
                console.error("❌ ETL job failed");
                result.errors.forEach((error) => {
                    console.error(`   ${error.message}`);
                });
                process.exit(1);
            }
        } catch (error) {
            console.error(
                "❌ ETL job failed:",
                error instanceof Error ? error.message : "Unknown error"
            );
            process.exit(1);
        }
    });

// Validate command
program
    .command("validate <configPath>")
    .description("Validate ETL configuration")
    .option("-l, --log-level <level>", "Set log level", "warn")
    .action((configPath, options) => {
        try {
            const result = validateConfig(configPath, options.logLevel);

            if (result.valid) {
                console.log("✅ Configuration is valid");
                process.exit(0);
            } else {
                console.error("❌ Configuration validation failed:");
                result.errors.forEach((error) => {
                    console.error(`   ${error}`);
                });
                process.exit(1);
            }
        } catch (error) {
            console.error(
                "❌ Validation failed:",
                error instanceof Error ? error.message : "Unknown error"
            );
            process.exit(1);
        }
    });

// List flows command
program
    .command("list-flows <configPath>")
    .description("List flows in a migration configuration")
    .action((configPath) => {
        try {
            const orchestrator = createEtlOrchestrator("warn");
            const flows = orchestrator.getFlowExecutionOrder(configPath);

            if (flows.length === 0) {
                console.log("No flows found");
                return;
            }

            console.log("📋 Flow execution order:");
            flows.forEach((flow, index) => {
                console.log(`   ${index + 1}. ${flow}`);
            });
        } catch (error) {
            console.error(
                "❌ Failed to list flows:",
                error instanceof Error ? error.message : "Unknown error"
            );
            process.exit(1);
        }
    });

// Test connections command
program
    .command("test-connections <configPath>")
    .description("Test all connections in configuration")
    .option("-l, --log-level <level>", "Set log level", "info")
    .action(async (configPath, options) => {
        try {
            const orchestrator = createEtlOrchestrator(options.logLevel);
            const config = orchestrator["configManager"].loadConfig(configPath);
            const results = await orchestrator.testConnections(config);

            console.log("🔗 Connection test results:");
            let allValid = true;

            for (const [name, isValid] of Object.entries(results)) {
                const status = isValid ? "✅" : "❌";
                console.log(`   ${status} ${name}`);
                if (!isValid) allValid = false;
            }

            if (allValid) {
                console.log("\n✅ All connections are valid");
                process.exit(0);
            } else {
                console.log("\n❌ Some connections failed");
                process.exit(1);
            }
        } catch (error) {
            console.error(
                "❌ Connection test failed:",
                error instanceof Error ? error.message : "Unknown error"
            );
            process.exit(1);
        }
    });

// Enhanced run command with streaming support
program
    .command("run-enhanced <configPath>")
    .description("Run ETL job with enhanced streaming orchestrator")
    .option(
        "-l, --log-level <level>",
        "Set log level (debug, info, warn, error)",
        "info"
    )
    .option(
        "--limit <number>",
        "Limit number of records (for testing)",
        parseInt
    )
    .option("--no-persistence", "Disable context persistence")
    .option(
        "--temp-dir <path>",
        "Custom temporary directory for context storage"
    )
    .option(
        "--max-memory <number>",
        "Maximum records in memory before persistence",
        parseInt
    )
    .action(async (configPath, options) => {
        console.log("🚀 Using enhanced orchestrator with streaming support");
        console.log(`🔧 Config: "${configPath}"`);

        try {
            const orchestrator = createEnhancedEtlOrchestrator(
                options.logLevel,
                {
                    enablePersistence: options.persistence !== false,
                    tempDirectory: options.tempDir,
                    maxMemoryRecords: options.maxMemory,
                }
            );

            const result = await orchestrator.executeFromFile(
                configPath,
                options.limit
            );

            if (result.success) {
                console.log("✅ Enhanced ETL job completed successfully");
                if ("migrationName" in result) {
                    console.log(`📊 Migration: ${result.migrationName}`);
                    console.log(
                        `📈 Total records: ${result.totalRecordsProcessed}`
                    );
                    console.log(`⏱️  Duration: ${result.totalDuration}ms`);
                    console.log(
                        `🔄 Flows completed: ${result.flowResults.length}`
                    );
                } else {
                    console.log(`📊 Flow: ${result.flowName}`);
                    console.log(
                        `📈 Records processed: ${result.recordsProcessed}`
                    );
                    console.log(`⏱️  Duration: ${result.duration}ms`);
                }

                // Show memory stats
                const activeContexts = orchestrator.getActiveContexts();
                if (activeContexts.length > 0) {
                    console.log("💾 Memory usage:");
                    activeContexts.forEach((jobName) => {
                        const stats = orchestrator.getMemoryStats(jobName);
                        console.log(
                            `   ${jobName}: ${stats.memoryUsageMB.toFixed(
                                2
                            )}MB, ${stats.totalRecords} records`
                        );
                    });
                }

                process.exit(0);
            } else {
                console.error("❌ Enhanced ETL job failed");
                result.errors.forEach((error) => {
                    console.error(`   ${error.message || error}`);
                });
                process.exit(1);
            }
        } catch (error) {
            console.error(
                "❌ Enhanced ETL job failed:",
                error instanceof Error ? error.message : "Unknown error"
            );
            process.exit(1);
        }
    });

// Test streaming functionality
program
    .command("test-streaming")
    .description("Run streaming functionality tests")
    .action(async () => {
        console.log("🧪 Running streaming functionality tests...");

        try {
            await runStreamingTests();
            console.log("✅ All streaming tests passed!");
            process.exit(0);
        } catch (error) {
            console.error(
                "❌ Streaming tests failed:",
                error instanceof Error ? error.message : "Unknown error"
            );
            process.exit(1);
        }
    });

// Info command
program
    .command("info")
    .description("Show version and system information")
    .action(() => {
        console.log("📦 Salesforce ETL Tool v2");
        console.log("🏗️  Simplified Architecture with Streaming Support");
        console.log(`📍 Node.js: ${process.version}`);
        console.log(`📍 Platform: ${process.platform}`);
        console.log("\n🚀 Features:");
        console.log("   • REST API and Bulk 2.0 loaders");
        console.log(
            "   • Simple, Join, Split, Lookup, and Aggregation transformations"
        );
        console.log(
            "   • Built-in expression functions (cleanText, formatDate, formatNumber)"
        );
        console.log("   • Cross-flow data linking");
        console.log("   • Consolidated CSV utilities");
        console.log("   • Simplified configuration schema");
        console.log("\n🌊 Streaming Features:");
        console.log("   • AugmentContextEngine for memory management");
        console.log("   • Streaming CSV processing for large files");
        console.log("   • Batch processing with configurable sizes");
        console.log("   • Context persistence for large datasets");
        console.log("   • Memory-efficient flow execution");
        console.log("\n🧹 Improvements in v2:");
        console.log("   • Removed over-engineered built-in transformers");
        console.log("   • Consolidated duplicate code");
        console.log("   • Simplified configuration with type-specific schemas");
        console.log(
            "   • Better expression evaluation with built-in functions"
        );
        console.log("   • Enhanced streaming support for large volumes");
    });

// Error handling
program.on("command:*", () => {
    console.error("❌ Invalid command. Use --help for available commands.");
    process.exit(1);
});

// Parse arguments
if (process.argv.length === 2) {
    program.help();
}

program.parse(process.argv);
