import * as fs from 'fs';
import * as path from 'path';
import * as yaml from 'js-yaml';
import { EtlConfig, SimpleJobConfig, MigrationConfig, validateConfig, isSimpleJobConfig, isMigrationConfig } from './schema';
import { Logger } from '../utils/logger';

/**
 * Simplified Configuration Manager for ETL v2
 * Removed complex inheritance features that weren't being used
 */
export class ConfigManager {
    constructor(private logger: Logger) {}

    /**
     * Load and validate configuration from a file
     */
    loadConfig(configPath: string): EtlConfig {
        this.logger.debug(`Loading configuration from: ${configPath}`);
        this.logger.debug(`Absolute path: ${path.resolve(configPath)}`);

        const config = this.loadConfigWithInheritance(configPath);

        // Validate configuration
        const errors = validateConfig(config);
        if (errors.length > 0) {
            throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
        }

        this.logger.info(`Configuration loaded successfully: ${this.getConfigName(config)}`);
        return config;
    }

    /**
     * Load configuration with inheritance support
     */
    private loadConfigWithInheritance(configPath: string, visitedPaths: Set<string> = new Set()): EtlConfig {
        const absolutePath = path.resolve(configPath);

        // Check for circular inheritance
        if (visitedPaths.has(absolutePath)) {
            throw new Error(`Circular inheritance detected: ${absolutePath}`);
        }
        visitedPaths.add(absolutePath);

        if (!fs.existsSync(absolutePath)) {
            throw new Error(`Configuration file not found: ${absolutePath}`);
        }

        const configContent = fs.readFileSync(absolutePath, 'utf8');
        let config: any;

        try {
            const ext = path.extname(absolutePath).toLowerCase();
            if (ext === '.yml' || ext === '.yaml') {
                config = yaml.load(configContent) as any;
            } else if (ext === '.json') {
                config = JSON.parse(configContent);
            } else {
                throw new Error(`Unsupported configuration file format: ${ext}`);
            }
        } catch (error) {
            throw new Error(`Failed to parse configuration file: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        // Check if this config extends another
        if (config.extends) {
            this.logger.debug(`Configuration extends: ${config.extends}`);
            const parentPath = path.resolve(path.dirname(absolutePath), config.extends);
            const parentConfig = this.loadConfigWithInheritance(parentPath, visitedPaths);
            config = this.mergeConfigurations(parentConfig, config);
            delete config.extends;
        }

        return config as EtlConfig;
    }

    /**
     * Deep merge two configurations (child overrides parent)
     */
    private mergeConfigurations(parent: any, child: any): any {
        if (typeof child !== 'object' || child === null) {
            return child;
        }

        if (typeof parent !== 'object' || parent === null) {
            return child;
        }

        if (Array.isArray(parent) && Array.isArray(child)) {
            return child; // Override array completely
        }

        const result: any = { ...parent };

        for (const key in child) {
            if (child.hasOwnProperty(key)) {
                if (typeof child[key] === 'object' && child[key] !== null && !Array.isArray(child[key])) {
                    result[key] = this.mergeConfigurations(parent[key], child[key]);
                } else {
                    result[key] = child[key];
                }
            }
        }

        return result;
    }

    /**
     * Get configuration name for logging
     */
    private getConfigName(config: EtlConfig): string {
        if (isSimpleJobConfig(config)) {
            return config.name;
        } else if (isMigrationConfig(config)) {
            return config.migration.name;
        }
        return 'Unknown';
    }

    /**
     * Get configuration type for orchestration
     */
    getConfigType(config: EtlConfig): 'simple' | 'migration' {
        return isSimpleJobConfig(config) ? 'simple' : 'migration';
    }

    /**
     * Validate configuration without loading
     */
    validateConfigFile(configPath: string): { valid: boolean; errors: string[] } {
        try {
            const config = this.loadConfig(configPath);
            const errors = validateConfig(config);
            return { valid: errors.length === 0, errors };
        } catch (error) {
            return { valid: false, errors: [error instanceof Error ? error.message : 'Unknown error'] };
        }
    }

    /**
     * List flows in a migration configuration
     */
    listFlows(configPath: string): string[] {
        const config = this.loadConfig(configPath);

        if (isSimpleJobConfig(config)) {
            return [config.name];
        } else if (isMigrationConfig(config)) {
            return Object.keys(config.flows);
        }

        return [];
    }

    /**
     * Get flow execution order for migration
     */
    getExecutionOrder(config: MigrationConfig): string[] {
        return config.migration.migrationOrder;
    }

    /**
     * Resolve flow dependencies
     */
    resolveDependencies(config: MigrationConfig): Map<string, string[]> {
        const dependencies = new Map<string, string[]>();

        for (const [flowName, flowConfig] of Object.entries(config.flows)) {
            dependencies.set(flowName, flowConfig.dependencies || []);
        }

        return dependencies;
    }

    /**
     * Validate flow dependencies don't have cycles
     */
    validateDependencies(config: MigrationConfig): string[] {
        const errors: string[] = [];
        const dependencies = this.resolveDependencies(config);
        const visiting = new Set<string>();
        const visited = new Set<string>();

        const hasCycle = (flowName: string): boolean => {
            if (visiting.has(flowName)) return true;
            if (visited.has(flowName)) return false;

            visiting.add(flowName);
            const flowDeps = dependencies.get(flowName) || [];

            for (const dep of flowDeps) {
                if (!dependencies.has(dep)) {
                    errors.push(`Flow "${flowName}" depends on non-existent flow "${dep}"`);
                    continue;
                }
                if (hasCycle(dep)) return true;
            }

            visiting.delete(flowName);
            visited.add(flowName);
            return false;
        };

        for (const flowName of dependencies.keys()) {
            if (hasCycle(flowName)) {
                errors.push(`Circular dependency detected involving flow "${flowName}"`);
                break;
            }
        }

        return errors;
    }
}
