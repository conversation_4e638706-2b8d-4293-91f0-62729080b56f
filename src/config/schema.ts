/**
 * Simplified Configuration Schema for ETL v2
 * Removed unused options and consolidated transformer types
 */

// Base interfaces
export interface ConnectionConfig {
    sfdxAlias?: string;
    username?: string;
    password?: string;
    securityToken?: string;
    accessToken?: string;
    instanceUrl?: string;
    apiVersion?: string;
}

export interface DataSourceConfig {
    type: "api" | "csv" | "flow-output";

    // API source fields
    object?: string;
    fields?: string[];
    whereClause?: string;
    query?: string; // For custom SOQL query

    // CSV source fields
    filePath?: string;
    filePattern?: string; // Glob pattern like "accounts_*.csv"
    directory?: string; // Directory to scan for files
    fileList?: string[]; // Explicit list of file paths
    sortFiles?: boolean; // Sort files alphabetically (default: true)
    maxFiles?: number; // Limit number of files to process
    addFileMetadata?: boolean; // Add file source metadata to records (default: true)
    splitFilesIntoJobs?: boolean; // Process each file as a separate job (default: false)

    // Flow output source fields
    sourceFlow?: string;
    sourceTarget?: string;

    // Common fields
    batchSize?: number;
    connection?: string;
}

// Simplified transformer configurations (type-specific)
export interface SimpleTransformConfig {
    type: "simple";
    mappings: Record<string, string>;
    filter?: string;
}

export interface JoinTransformConfig {
    type: "join";
    primarySource: string;
    joins: Array<{
        sourceAlias: string;
        joinType: "left" | "inner" | "right";
        on: Array<{
            primaryField: string;
            joinField: string;
        }>;
    }>;
    mappings: Record<string, string>;
    filter?: string;
}

export interface SplitTransformConfig {
    type: "split";
    outputTargets: Array<{
        target: string;
        condition: string;
        mappings: Record<string, string>;
    }>;
}

export interface LookupTransformConfig {
    type: "lookup";
    sourceAlias?: string; // Alias of the lookup data source
    conditions: Array<{
        type:
            | "dateRange"
            | "equals"
            | "greaterThan"
            | "lessThan"
            | "greaterThanOrEquals"
            | "lessThanOrEquals"
            | "boolean";
        sourceField: string;
        lookupField?: string;
        lookupStartField?: string;
        lookupEndField?: string;
        value?: any;
    }>;
    resultField: string;
    resultMappings?: Record<string, string>;
    multipleMatchStrategy?: "first" | "last" | "error";
    noMatchStrategy?: "null" | "error" | "skip";
    mappings: Record<string, string>;
    filter?: string;
}

export interface AggregationTransformConfig {
    type: "aggregation";
    groupBy: string[];
    aggregates: Array<{
        field: string;
        operation: "sum" | "count" | "avg" | "min" | "max" | "concat";
        targetField: string;
        options?: {
            separator?: string;
            distinct?: boolean;
        };
    }>;
    mappings: Record<string, string>;
    filter?: string;
}

export type TransformConfig =
    | SimpleTransformConfig
    | JoinTransformConfig
    | SplitTransformConfig
    | LookupTransformConfig
    | AggregationTransformConfig;

export interface TargetConfig {
    name: string;
    object: string;
    connection: string;
    operation: "insert" | "update" | "upsert" | "delete";
    loadStrategy: "rest" | "bulk2" | "csv-export";
    externalId?: string;
    batchSize?: number;
    filePath?: string; // For CSV export
    bulkPollIntervalMs?: number;
    bulkPollTimeoutMs?: number;
}

export interface FlowConfig {
    name: string;
    description?: string;

    // Source configuration
    sources: Array<{
        name: string;
        alias?: string;
    }>;

    // Transform configuration
    transform: TransformConfig;

    // Target configuration
    targets: TargetConfig[];

    // Flow dependencies
    dependencies?: string[];

    // Debug options
    debug?: {
        saveTransformerCsv?: boolean;
        saveExtractorCsv?: boolean;
    };
}

// Root configuration types
export interface SimpleJobConfig {
    name: string;
    source: DataSourceConfig & { connection: string };
    transform: TransformConfig;
    target: TargetConfig;
    connections?: Record<string, ConnectionConfig>;
    debug?: {
        saveTransformerCsv?: boolean;
        saveExtractorCsv?: boolean;
    };
    extends?: string;
}

export interface MigrationConfig {
    migration: {
        name: string;
        version: string;
        migrationOrder: string[];
        globalVariables?: Record<string, any>;
        continueOnError?: boolean;
    };

    dataSources: Record<string, DataSourceConfig>;
    flows: Record<string, FlowConfig>;
    connections: Record<string, ConnectionConfig>;
    extends?: string;
}

// Union type for all configuration types
export type EtlConfig = SimpleJobConfig | MigrationConfig;

// Type guards
export function isSimpleJobConfig(
    config: EtlConfig
): config is SimpleJobConfig {
    return (
        "source" in config &&
        "transform" in config &&
        "target" in config &&
        !("migration" in config)
    );
}

export function isMigrationConfig(
    config: EtlConfig
): config is MigrationConfig {
    return (
        "migration" in config && "dataSources" in config && "flows" in config
    );
}

// Transform type guards
export function isSimpleTransform(
    transform: TransformConfig
): transform is SimpleTransformConfig {
    return transform.type === "simple";
}

export function isJoinTransform(
    transform: TransformConfig
): transform is JoinTransformConfig {
    return transform.type === "join";
}

export function isSplitTransform(
    transform: TransformConfig
): transform is SplitTransformConfig {
    return transform.type === "split";
}

export function isLookupTransform(
    transform: TransformConfig
): transform is LookupTransformConfig {
    return transform.type === "lookup";
}

export function isAggregationTransform(
    transform: TransformConfig
): transform is AggregationTransformConfig {
    return transform.type === "aggregation";
}

// Basic validation
export function validateConfig(config: EtlConfig): string[] {
    const errors: string[] = [];

    if (isSimpleJobConfig(config)) {
        if (!config.name) errors.push("Simple job must have a name");
        if (!config.source) errors.push("Simple job must have a source");
        if (!config.transform) errors.push("Simple job must have a transform");
        if (!config.target) errors.push("Simple job must have a target");
    } else if (isMigrationConfig(config)) {
        if (!config.migration?.name) errors.push("Migration must have a name");
        if (!config.migration?.migrationOrder?.length)
            errors.push("Migration must have migrationOrder");
        if (!config.dataSources) errors.push("Migration must have dataSources");
        if (!config.flows) errors.push("Migration must have flows");
        if (!config.connections) errors.push("Migration must have connections");
    } else {
        errors.push("Invalid configuration format");
    }

    return errors;
}
