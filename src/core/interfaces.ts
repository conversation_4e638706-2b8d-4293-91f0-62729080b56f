/**
 * Core interfaces for ETL v2 components
 * Simplified and cleaned up from original
 */

import { DataSourceConfig, TransformConfig, TargetConfig } from '../config/schema';

/**
 * Base extractor interface
 */
export interface Extractor {
    extract(): AsyncGenerator<Record<string, any>[], void, unknown>;
    getRecordCount?(): Promise<number>;
}

/**
 * Base transformer interface
 */
export interface Transformer {
    transform(records: Record<string, any>[]): Promise<Record<string, any>[]>;
}

/**
 * Base loader interface
 */
export interface Loader {
    load(records: Record<string, any>[]): Promise<LoadResult>;
    finalize?(): Promise<LoadResult>;
}

/**
 * Load result interface
 */
export interface LoadResult {
    recordsProcessed: number;
    recordsSucceeded: number;
    recordsFailed: number;
    errors: any[];
    duration?: number;
    results?: any[];
    success: boolean;
}

/**
 * Job execution context
 */
export interface JobContext {
    jobName: string;
    flowOutputs: Map<string, Record<string, any>[]>;
    globalVariables: Record<string, any>;
    recordLimit?: number;
}

/**
 * Flow execution result
 */
export interface FlowResult {
    flowName: string;
    success: boolean;
    recordsProcessed: number;
    duration: number;
    outputs: Record<string, Record<string, any>[]>;
    errors: any[];
}

/**
 * Migration execution result
 */
export interface MigrationResult {
    migrationName: string;
    success: boolean;
    totalRecordsProcessed: number;
    totalDuration: number;
    flowResults: FlowResult[];
    errors: any[];
}
