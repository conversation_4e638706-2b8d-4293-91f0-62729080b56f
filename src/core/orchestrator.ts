import {
    Etl<PERSON>onfig,
    SimpleJobConfig,
    MigrationConfig,
    FlowConfig,
    isSimpleJobConfig,
    isMigrationConfig,
    isJoinTransform,
    isSplitTransform,
    isLookupTransform,
    isAggregationTransform,
    TargetConfig,
    ConnectionConfig,
    DataSourceConfig,
} from "../config/schema";
import { ConfigManager } from "../config/manager";
import { ConnectionService } from "../services/connectionService";
import { ExtractorFactory, EnhancedCsvExtractor } from "../extractors";
import { TransformerFactory } from "../transformers/transformerFactory";
import { LoaderFactory } from "../loaders/loaderFactory";
import { Logger } from "../utils";
import {
    FlowResult,
    MigrationResult,
    Extractor,
    Transformer,
    Loader,
} from "./interfaces";
import { JoinTransformer } from "../transformers/joinTransformer";
import { SplitTransformer } from "../transformers/splitTransformer";
import { LookupTransformer } from "../transformers/lookupTransformer";
import { basename } from "path";

// Added JobContext interface definition
interface JobContext {
    jobName: string;
    flowOutputs: Map<string, any>;
    globalVariables: Record<string, any>;
    recordLimit?: number;
}

/**
 * Simplified ETL Orchestrator for v2
 * Removed complexity while keeping core functionality
 */
export class EtlOrchestrator {
    constructor(
        private configManager: ConfigManager,
        private connectionService: ConnectionService,
        private extractorFactory: ExtractorFactory,
        private transformerFactory: TransformerFactory,
        private loaderFactory: LoaderFactory,
        private logger: Logger
    ) {}

    /**
     * Execute ETL job from configuration file
     */
    async executeFromFile(
        configPath: string,
        recordLimit?: number
    ): Promise<FlowResult | MigrationResult> {
        const config = this.configManager.loadConfig(configPath);
        return this.execute(config, recordLimit);
    }

    /**
     * Execute ETL job from configuration object
     */
    async execute(
        config: EtlConfig,
        recordLimit?: number
    ): Promise<FlowResult | MigrationResult> {
        if (isSimpleJobConfig(config)) {
            return this.executeSimpleJob(config, recordLimit);
        } else if (isMigrationConfig(config)) {
            return this.executeMigration(config, recordLimit);
        } else {
            this.logger.error(
                "Invalid configuration type provided to orchestrator."
            );
            throw new Error("Invalid configuration type");
        }
    }

    /**
     * Execute a simple ETL job
     */
    private async executeSimpleJob(
        config: SimpleJobConfig,
        recordLimit?: number
    ): Promise<FlowResult> {
        const startTime = Date.now();
        const mainJobName =
            config.name.includes("-") && config.source.filePath
                ? config.name.substring(0, config.name.lastIndexOf("-"))
                : config.name;
        const isPotentiallySubJob = config.name !== mainJobName;
        if (!isPotentiallySubJob) {
            this.logger.logFlowStart(config.name);
        }

        try {
            const context: JobContext = {
                jobName: config.name,
                flowOutputs: new Map(),
                globalVariables: {},
                recordLimit,
            };

            const connections = config.connections || {};
            const extractor = this.extractorFactory.createExtractor(
                config.source,
                connections,
                context,
                recordLimit,
                config.debug
            );

            if (
                config.source.type === "csv" &&
                config.source.splitFilesIntoJobs &&
                extractor instanceof EnhancedCsvExtractor
            ) {
                const filePaths = extractor.getFilePaths();
                if (filePaths.length > 1) {
                    this.logger.info(
                        `Splitting job '${mainJobName}' into ${filePaths.length} sub-jobs.`
                    );
                    const subJobResults: FlowResult[] = [];
                    let overallSuccess = true;
                    let totalRecordsProcessedAllSubJobs = 0;
                    const aggregatedOutputs: Record<string, any[]> = {};

                    for (const filePath of filePaths) {
                        const subJobFileName = basename(filePath);
                        const subJobConfig: SimpleJobConfig = JSON.parse(
                            JSON.stringify(config)
                        );
                        subJobConfig.source.filePath = filePath;
                        subJobConfig.source.filePattern = undefined;
                        subJobConfig.source.fileList = undefined;
                        subJobConfig.source.directory = undefined;
                        subJobConfig.source.splitFilesIntoJobs = false;
                        subJobConfig.name = `${mainJobName}-${subJobFileName}`;

                        this.logger.logFlowStart(subJobConfig.name);
                        const subJobResult = await this.executeSimpleJob(
                            subJobConfig,
                            recordLimit
                        );
                        subJobResults.push(subJobResult);
                        totalRecordsProcessedAllSubJobs +=
                            subJobResult.recordsProcessed || 0;
                        if (!subJobResult.success) overallSuccess = false;
                        if (subJobResult.outputs) {
                            for (const key in subJobResult.outputs) {
                                if (subJobResult.outputs.hasOwnProperty(key)) {
                                    if (!aggregatedOutputs[key])
                                        aggregatedOutputs[key] = [];
                                    const outputData =
                                        subJobResult.outputs[key];
                                    if (Array.isArray(outputData))
                                        aggregatedOutputs[key].push(
                                            ...outputData
                                        );
                                    else if (
                                        outputData !== null &&
                                        outputData !== undefined
                                    )
                                        aggregatedOutputs[key].push(outputData);
                                }
                            }
                        }
                    }
                    const overallDuration = Date.now() - startTime;
                    this.logger.info(
                        `Completed all ${filePaths.length} sub-jobs for '${mainJobName}'. Total records: ${totalRecordsProcessedAllSubJobs}. Duration: ${overallDuration}ms`
                    );
                    const finalResult: FlowResult = {
                        flowName: mainJobName,
                        success: overallSuccess,
                        recordsProcessed: totalRecordsProcessedAllSubJobs,
                        duration: overallDuration,
                        outputs: aggregatedOutputs,
                        errors: subJobResults
                            .flatMap((r) => r.errors || [])
                            .filter((e) => e !== undefined) as Error[],
                    };
                    this.logger.logFlowComplete(
                        mainJobName,
                        finalResult.recordsProcessed,
                        finalResult.duration
                    );
                    return finalResult;
                }
            }

            const transformer = this.transformerFactory.createTransformer(
                config.transform,
                context,
                config.debug
            );
            const loader = await this.loaderFactory.createLoader(
                config.target,
                connections
            );
            const result = await this.executeFlow(
                config.name,
                extractor,
                transformer,
                loader,
                context
            );
            const duration = Date.now() - startTime;
            this.logger.logFlowComplete(
                config.name,
                result.recordsProcessed,
                duration
            );
            return { ...result, duration };
        } catch (error) {
            const duration = Date.now() - startTime;
            const jobNameForError =
                config.source.type === "csv" &&
                config.source.splitFilesIntoJobs &&
                !config.source.filePath
                    ? mainJobName
                    : config.name;
            this.logger.logFlowError(
                jobNameForError,
                error instanceof Error ? error : new Error(String(error))
            );
            return {
                flowName: jobNameForError,
                success: false,
                recordsProcessed: 0,
                duration,
                outputs: {},
                errors: [
                    error instanceof Error ? error : new Error(String(error)),
                ],
            };
        }
    }

    /**
     * Execute a complex migration with multiple flows
     */
    private async executeMigration(
        config: MigrationConfig,
        recordLimit?: number
    ): Promise<MigrationResult> {
        const startTime = Date.now();
        this.logger.info(`Starting migration: ${config.migration.name}`);
        const results: FlowResult[] = [];
        let overallSuccess = true;
        const context: JobContext = {
            jobName: config.migration.name,
            flowOutputs: new Map(),
            globalVariables: config.migration.globalVariables ?? {},
            recordLimit,
        };
        const executionOrder = this.configManager.getExecutionOrder(config);
        const executedFlows = new Set<string>();

        for (const flowKey of executionOrder) {
            const flowConfig = config.flows[flowKey];
            if (!flowConfig) {
                this.logger.warn(
                    `Flow configuration not found: ${flowKey}, skipping.`
                );
                continue;
            }
            let dependenciesMet = true;
            if (flowConfig.dependencies) {
                for (const depName of flowConfig.dependencies) {
                    if (!executedFlows.has(depName)) {
                        this.logger.warn(
                            `Dependency ${depName} for flow ${flowKey} not met (not executed), skipping.`
                        );
                        dependenciesMet = false;
                        break;
                    }
                    const depResult = results.find(
                        (r) => r.flowName === depName
                    );
                    if (!depResult || !depResult.success) {
                        this.logger.warn(
                            `Dependency ${depName} for flow ${flowKey} failed or was skipped, skipping current flow.`
                        );
                        dependenciesMet = false;
                        break;
                    }
                }
            }
            if (!dependenciesMet) {
                if (!config.migration.continueOnError) overallSuccess = false;
                results.push({
                    flowName: flowKey,
                    success: false,
                    recordsProcessed: 0,
                    duration: 0,
                    outputs: {},
                    errors: [
                        {
                            message:
                                "Skipped due to unmet/failed dependencies.",
                        },
                    ],
                });
                if (!config.migration.continueOnError) break;
                continue;
            }
            const flowResult = await this.executeFlowFromConfig(
                flowConfig,
                config,
                context,
                flowKey
            );
            results.push(flowResult);
            executedFlows.add(flowKey);
            if (!flowResult.success) {
                overallSuccess = false;
                if (!config.migration.continueOnError) {
                    this.logger.error(
                        `Flow '${flowKey}' failed. Halting migration.`
                    );
                    break;
                }
            }
        }
        const duration = Date.now() - startTime;
        const totalRecords = results.reduce(
            (sum, r) => sum + (r.recordsProcessed || 0),
            0
        );
        this.logger.info(
            `Migration '${config.migration.name}' finished. Success: ${overallSuccess}. Records: ${totalRecords}. Duration: ${duration}ms`
        );
        return {
            migrationName: config.migration.name,
            success: overallSuccess,
            totalRecordsProcessed: totalRecords,
            totalDuration: duration,
            flowResults: results,
            errors: results
                .flatMap((r) => r.errors || [])
                .filter((e) => e) as Error[],
        };
    }

    /**
     * Execute a single flow from migration configuration
     */
    private async executeFlowFromConfig(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        flowKey: string
    ): Promise<FlowResult> {
        const originalFlowName = flowConfig.name;
        let splittingSourceRef: { name: string; alias?: string } | undefined;
        let splittingExtractorInstance: EnhancedCsvExtractor | undefined;

        for (const sourceRef of flowConfig.sources) {
            const dsConfig = migrationConfig.dataSources[sourceRef.name];
            if (
                dsConfig &&
                dsConfig.type === "csv" &&
                dsConfig.splitFilesIntoJobs
            ) {
                const tempExtractor = this.extractorFactory.createExtractor(
                    dsConfig,
                    migrationConfig.connections,
                    { ...context, jobName: `${originalFlowName}-filescan` },
                    undefined,
                    flowConfig.debug
                );
                if (
                    tempExtractor instanceof EnhancedCsvExtractor &&
                    tempExtractor.getFilePaths().length > 1
                ) {
                    splittingSourceRef = sourceRef;
                    splittingExtractorInstance = tempExtractor;
                    this.logger.info(
                        `Flow '${originalFlowName}' will be split by file for source '${sourceRef.name}'.`
                    );
                    break;
                }
            }
        }

        if (splittingSourceRef && splittingExtractorInstance) {
            const filePaths = splittingExtractorInstance.getFilePaths();
            const subFlowResults: FlowResult[] = [];
            let overallSuccess = true;
            let totalRecordsProcessedAllSubFlows = 0;
            const aggregatedLoaderOutputs: Record<string, any[]> = {};
            const overallStartTime = Date.now();
            this.logger.info(
                `Starting grouped flow '${originalFlowName}' (split into ${filePaths.length} file executions).`
            );

            for (const filePath of filePaths) {
                const subFlowFileName = basename(filePath);
                const subFlowName = `${originalFlowName}-${subFlowFileName}`;
                this.logger.info(`Preparing sub-flow: ${subFlowName}`);
                const subFlowConfig: FlowConfig = JSON.parse(
                    JSON.stringify(flowConfig)
                );
                subFlowConfig.name = subFlowName;
                const subDataSources: Record<string, DataSourceConfig> =
                    JSON.parse(JSON.stringify(migrationConfig.dataSources));
                const currentSplittingDsCopy =
                    subDataSources[splittingSourceRef.name];
                currentSplittingDsCopy.filePath = filePath;
                currentSplittingDsCopy.filePattern = undefined;
                currentSplittingDsCopy.fileList = undefined;
                currentSplittingDsCopy.directory = undefined;
                currentSplittingDsCopy.splitFilesIntoJobs = false;
                const subContext: JobContext = {
                    ...context,
                    jobName: subFlowName,
                    flowOutputs: new Map(context.flowOutputs),
                };
                this.logger.logFlowStart(subFlowName);
                const subFlowStartTime = Date.now();
                let subFlowResult: FlowResult;
                try {
                    switch (subFlowConfig.transform.type) {
                        case "simple":
                            subFlowResult = await this.executeSimpleFlow(
                                subFlowConfig,
                                migrationConfig,
                                subContext,
                                subDataSources
                            );
                            break;
                        case "join":
                            subFlowResult = await this.executeJoinFlow(
                                subFlowConfig,
                                migrationConfig,
                                subContext,
                                subDataSources
                            );
                            break;
                        case "split":
                            subFlowResult = await this.executeSplitFlow(
                                subFlowConfig,
                                migrationConfig,
                                subContext,
                                subDataSources
                            );
                            break;
                        case "lookup":
                            subFlowResult = await this.executeLookupFlow(
                                subFlowConfig,
                                migrationConfig,
                                subContext,
                                subDataSources
                            );
                            break;
                        case "aggregation":
                            subFlowResult = await this.executeAggregationFlow(
                                subFlowConfig,
                                migrationConfig,
                                subContext,
                                subDataSources
                            );
                            break;
                        default:
                            throw new Error(
                                `Unsupported transform type: ${
                                    (subFlowConfig.transform as any).type
                                }`
                            );
                    }
                } catch (error) {
                    const duration = Date.now() - subFlowStartTime;
                    this.logger.logFlowError(
                        subFlowName,
                        error instanceof Error
                            ? error
                            : new Error(String(error))
                    );
                    subFlowResult = {
                        flowName: subFlowName,
                        success: false,
                        recordsProcessed: 0,
                        duration,
                        outputs: {},
                        errors: [
                            error instanceof Error
                                ? error
                                : new Error(String(error)),
                        ],
                    };
                }
                if (subFlowResult.duration === undefined)
                    subFlowResult.duration = Date.now() - subFlowStartTime;
                subFlowResults.push(subFlowResult);
                totalRecordsProcessedAllSubFlows +=
                    subFlowResult.recordsProcessed || 0;
                if (!subFlowResult.success) overallSuccess = false;
                if (subFlowResult.outputs) {
                    for (const outKey in subFlowResult.outputs) {
                        if (subFlowResult.outputs.hasOwnProperty(outKey)) {
                            if (!aggregatedLoaderOutputs[outKey])
                                aggregatedLoaderOutputs[outKey] = [];
                            const outData = subFlowResult.outputs[outKey];
                            if (Array.isArray(outData))
                                aggregatedLoaderOutputs[outKey].push(
                                    ...outData
                                );
                            else if (outData !== null && outData !== undefined)
                                aggregatedLoaderOutputs[outKey].push(outData);
                        }
                    }
                }
                this.logger.logFlowComplete(
                    subFlowName,
                    subFlowResult.recordsProcessed,
                    subFlowResult.duration
                );
            }
            const overallDuration = Date.now() - overallStartTime;
            this.logger.info(
                `Completed all ${filePaths.length} sub-flows for '${originalFlowName}'. Total records: ${totalRecordsProcessedAllSubFlows}. Duration: ${overallDuration}ms`
            );
            const finalResult: FlowResult = {
                flowName: flowKey,
                success: overallSuccess,
                recordsProcessed: totalRecordsProcessedAllSubFlows,
                duration: overallDuration,
                outputs: aggregatedLoaderOutputs,
                errors: subFlowResults
                    .flatMap((r) => r.errors || [])
                    .filter((e) => e) as Error[],
            };
            this.logger.logFlowComplete(
                originalFlowName,
                finalResult.recordsProcessed,
                finalResult.duration
            );
            context.flowOutputs.set(
                originalFlowName,
                finalResult.outputs as any
            );
            return finalResult;
        } else {
            this.logger.logFlowStart(originalFlowName);
            const startTime = Date.now();
            let result: FlowResult;
            try {
                switch (flowConfig.transform.type) {
                    case "simple":
                        result = await this.executeSimpleFlow(
                            flowConfig,
                            migrationConfig,
                            context,
                            migrationConfig.dataSources
                        );
                        break;
                    case "join":
                        result = await this.executeJoinFlow(
                            flowConfig,
                            migrationConfig,
                            context,
                            migrationConfig.dataSources
                        );
                        break;
                    case "split":
                        result = await this.executeSplitFlow(
                            flowConfig,
                            migrationConfig,
                            context,
                            migrationConfig.dataSources
                        );
                        break;
                    case "lookup":
                        result = await this.executeLookupFlow(
                            flowConfig,
                            migrationConfig,
                            context,
                            migrationConfig.dataSources
                        );
                        break;
                    case "aggregation":
                        result = await this.executeAggregationFlow(
                            flowConfig,
                            migrationConfig,
                            context,
                            migrationConfig.dataSources
                        );
                        break;
                    default:
                        throw new Error(
                            `Unsupported transform type: ${
                                (flowConfig.transform as any).type
                            }`
                        );
                }
                result.duration = Date.now() - startTime;
                this.logger.logFlowComplete(
                    originalFlowName,
                    result.recordsProcessed,
                    result.duration
                );
                context.flowOutputs.set(
                    originalFlowName,
                    result.outputs as any
                );
                return { ...result, flowName: flowKey };
            } catch (error) {
                const duration = Date.now() - startTime;
                this.logger.logFlowError(
                    originalFlowName,
                    error instanceof Error ? error : new Error(String(error))
                );
                const errorResult: FlowResult = {
                    flowName: flowKey,
                    success: false,
                    recordsProcessed: 0,
                    duration,
                    outputs: {},
                    errors: [
                        error instanceof Error
                            ? error
                            : new Error(String(error)),
                    ],
                };
                context.flowOutputs.set(
                    originalFlowName,
                    errorResult.outputs as any
                );
                return errorResult;
            }
        }
    }

    /**
     * Execute a simple flow (single source → transform → targets)
     */
    private async executeSimpleFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, DataSourceConfig>
    ): Promise<FlowResult> {
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );
        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias =
            primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        if (!primaryExtractor)
            throw new Error(
                `Primary extractor '${primaryExtractorAlias}' not found for simple flow '${flowConfig.name}'.`
            );
        for (const [alias, extractor] of extractors) {
            if (alias !== primaryExtractorAlias) {
                const outputData: Record<string, any>[] = [];
                for await (const batchData of extractor.extract()) {
                    const filteredBatch = batchData.filter(
                        (item): item is Record<string, any> =>
                            item !== null && item !== undefined
                    );
                    for (const item of filteredBatch) {
                        outputData.push(item);
                    }
                }
                context.flowOutputs.set(alias, outputData);
            }
        }
        const transformer = this.transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables:
                    migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );
        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );
        const records: Record<string, any>[] = [];
        for await (const batch of primaryExtractor.extract()) {
            const filteredBatch = batch.filter(
                (item): item is Record<string, any> =>
                    item !== null && item !== undefined
            );
            for (const item of filteredBatch) {
                records.push(item);
            }
        }
        const transformedRecords = await transformer.transform(records);
        const loadResult = await this.loadToMultipleTargets(
            transformedRecords,
            flowConfig.targets,
            migrationConfig.connections,
            loaders
        );
        return {
            flowName: flowConfig.name,
            success: loadResult.success,
            recordsProcessed: loadResult.totalRecords,
            outputs: loadResult.outputs,
            errors: loadResult.errors,
            duration: 0,
        };
    }

    /**
     * Execute a join flow (multiple sources → join → targets)
     */
    private async executeJoinFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, DataSourceConfig>
    ): Promise<FlowResult> {
        if (!isJoinTransform(flowConfig.transform))
            throw new Error("Invalid transform for executeJoinFlow");
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );
        const joinTransformConfig = flowConfig.transform;
        const primarySourceAlias = joinTransformConfig.primarySource;
        if (!extractors.has(primarySourceAlias))
            throw new Error(
                `Primary extractor '${primarySourceAlias}' not found for join flow '${flowConfig.name}'.`
            );
        for (const [alias, extractor] of extractors) {
            const outputData: Record<string, any>[] = [];
            for await (const batchData of extractor.extract()) {
                const filteredBatch = batchData.filter(
                    (item): item is Record<string, any> =>
                        item !== null && item !== undefined
                );
                for (const item of filteredBatch) {
                    outputData.push(item);
                }
            }
            context.flowOutputs.set(alias, outputData);
        }
        const transformer = this.transformerFactory.createTransformer(
            joinTransformConfig,
            {
                ...context,
                globalVariables:
                    migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );
        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );
        const primaryRecords =
            context.flowOutputs.get(primarySourceAlias) || [];
        const transformedRecords = await transformer.transform(primaryRecords);
        const loadResult = await this.loadToMultipleTargets(
            transformedRecords,
            flowConfig.targets,
            migrationConfig.connections,
            loaders
        );
        return {
            flowName: flowConfig.name,
            success: loadResult.success,
            recordsProcessed: loadResult.totalRecords,
            outputs: loadResult.outputs,
            errors: loadResult.errors,
            duration: 0,
        };
    }

    /**
     * Execute a split flow (single source → split → multiple targets)
     */
    private async executeSplitFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, DataSourceConfig>
    ): Promise<FlowResult> {
        if (!isSplitTransform(flowConfig.transform))
            throw new Error("Invalid transform for executeSplitFlow");
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );
        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias =
            primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        if (!primaryExtractor)
            throw new Error(
                `Primary extractor '${primaryExtractorAlias}' not found for split flow '${flowConfig.name}'.`
            );
        const transformer = this.transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables:
                    migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );
        const allPossibleLoaders =
            await this.loaderFactory.createMultipleLoaders(
                flowConfig.targets,
                migrationConfig.connections
            );
        const records: Record<string, any>[] = [];
        for await (const batch of primaryExtractor.extract()) {
            const filteredBatch = batch.filter(
                (item): item is Record<string, any> =>
                    item !== null && item !== undefined
            );
            for (const item of filteredBatch) {
                records.push(item);
            }
        }
        const splitOutputs = (await transformer.transform(
            records
        )) as unknown as Map<string, Record<string, any>[]>;

        const loadResult = await this.loadToTargetsFromMap(
            splitOutputs,
            flowConfig.targets,
            migrationConfig.connections,
            allPossibleLoaders
        );

        // Load all extractor outputs into context.flowOutputs
        for (const [alias, extractor] of extractors) {
            const outputData: Record<string, any>[] = [];
            for await (const batchData of extractor.extract()) {
                const filteredBatch = batchData.filter(
                    (item): item is Record<string, any> =>
                        item !== null && item !== undefined
                );
                for (const item of filteredBatch) {
                    outputData.push(item);
                }
            }
            context.flowOutputs.set(alias, outputData);
        }

        return {
            flowName: flowConfig.name,
            success: loadResult.success,
            recordsProcessed: loadResult.totalRecords,
            outputs: loadResult.outputs,
            errors: loadResult.errors,
            duration: 0,
        };
    }

    /**
     * Execute a lookup flow
     */
    private async executeLookupFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, DataSourceConfig>
    ): Promise<FlowResult> {
        if (!isLookupTransform(flowConfig.transform)) {
            throw new Error("Invalid transform type for executeLookupFlow");
        }
        const lookupTransformConfig = flowConfig.transform;

        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias =
            primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        if (!primaryExtractor) {
            throw new Error(
                `Primary extractor '${primaryExtractorAlias}' not found for lookup flow '${flowConfig.name}'.`
            );
        }

        for (const [alias, extractor] of extractors) {
            context.flowOutputs.set(alias, await extractor.extract());
        }

        const transformer = this.transformerFactory.createTransformer(
            lookupTransformConfig,
            {
                ...context,
                globalVariables:
                    migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        const primaryRecords =
            context.flowOutputs.get(primaryExtractorAlias) || [];
        const transformedRecords = await transformer.transform(primaryRecords);

        const loadResult = await this.loadToMultipleTargets(
            transformedRecords,
            flowConfig.targets,
            migrationConfig.connections,
            loaders
        );

        return {
            flowName: flowConfig.name,
            success: loadResult.success,
            recordsProcessed: loadResult.totalRecords,
            outputs: loadResult.outputs,
            errors: loadResult.errors,
            duration: 0,
        };
    }

    /**
     * Execute an aggregation flow
     */
    private async executeAggregationFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, DataSourceConfig>
    ): Promise<FlowResult> {
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias =
            primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        if (!primaryExtractor) {
            throw new Error(
                `Primary extractor '${primaryExtractorAlias}' not found for aggregation flow '${flowConfig.name}'.`
            );
        }

        const transformer = this.transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables:
                    migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        const records: Record<string, any>[] = [];
        for await (const batch of primaryExtractor.extract()) {
            const filteredBatch = batch.filter(
                (item): item is Record<string, any> =>
                    item !== null && item !== undefined
            );
            for (const item of filteredBatch) {
                records.push(item);
            }
        }
        const transformedRecords = await transformer.transform(records);

        const loadResult = await this.loadToMultipleTargets(
            transformedRecords,
            flowConfig.targets,
            migrationConfig.connections,
            loaders
        );

        return {
            flowName: flowConfig.name,
            success: loadResult.success,
            recordsProcessed: loadResult.totalRecords,
            outputs: loadResult.outputs,
            errors: loadResult.errors,
            duration: 0,
        };
    }

    /**
     * Execute core ETL flow (extract → transform → load)
     */
    private async executeFlow(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loader: Loader,
        context: JobContext
    ): Promise<FlowResult> {
        const startTime = Date.now();
        let totalRecords = 0;
        const allResults: any[] = [];
        const allErrors: any[] = [];

        try {
            for await (const batch of extractor.extract()) {
                const transformedBatch = await transformer.transform(batch);
                const loadResult = await loader.load(transformedBatch);

                totalRecords += transformedBatch.length;

                if (!loader.finalize) {
                    allResults.push(...(loadResult.results ?? []));
                    allErrors.push(...loadResult.errors);
                }

                this.logger.logBatchProgress(
                    `${flowName} processing`,
                    totalRecords,
                    totalRecords
                );
            }

            if (loader.finalize) {
                const finalizeResult = await loader.finalize();
                allResults.push(...(finalizeResult.results ?? []));
                allErrors.push(...finalizeResult.errors);
            }

            const duration = Date.now() - startTime;
            const success = allErrors.length === 0;

            return {
                flowName,
                success,
                recordsProcessed: totalRecords,
                duration,
                outputs: {
                    default: allResults,
                },
                errors: allErrors,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Flow execution failed: ${flowName}`, error);

            return {
                flowName,
                success: false,
                recordsProcessed: totalRecords,
                duration,
                outputs: {},
                errors: [
                    {
                        message:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    },
                ],
            };
        }
    }

    /**
     * Load data to multiple targets
     */
    private async loadToMultipleTargets(
        data: Record<string, any>[],
        targets: TargetConfig[],
        connections: Record<string, ConnectionConfig>,
        loaders: Map<string, Loader>
    ): Promise<{
        success: boolean;
        totalRecords: number;
        duration: number;
        outputs: Record<string, any[]>;
        errors: any[];
    }> {
        const startTime = Date.now();
        const loadResults: any[] = [];
        const outputs: Record<string, any[]> = {};
        let totalRecords = 0;

        for (const targetConfig of targets) {
            const loader = loaders.get(targetConfig.name);
            if (!loader) {
                this.logger.warn(
                    `Loader not found for target: ${targetConfig.name}. Skipping.`
                );
                continue;
            }
            const loadResult = await loader.load(data);

            let finalResult = loadResult;
            if (loader.finalize) {
                finalResult = await loader.finalize();
            }

            loadResults.push(finalResult);
            outputs[targetConfig.name] = data;
            totalRecords += data.length;
        }

        const duration = Date.now() - startTime;
        const allSuccessful = loadResults.every((r) => r.success);
        const allErrors = loadResults.flatMap((r) => r.errors);

        return {
            success: allSuccessful,
            totalRecords,
            duration,
            outputs,
            errors: allErrors,
        };
    }

    /**
     * Load data to targets from split transformer output map
     */
    private async loadToTargetsFromMap(
        targetOutputs: Map<string, Record<string, any>[]>,
        targets: TargetConfig[],
        connections: Record<string, ConnectionConfig>,
        loaders: Map<string, Loader>
    ): Promise<{
        success: boolean;
        totalRecords: number;
        duration: number;
        outputs: Record<string, any[]>;
        errors: any[];
    }> {
        const startTime = Date.now();
        const loadResults: any[] = [];
        const outputs: Record<string, any[]> = {};
        let totalRecords = 0;

        for (const [targetName, records] of targetOutputs.entries()) {
            const targetConfig = targets.find((t) => t.name === targetName);
            const loader = loaders.get(targetName);

            if (!loader || !targetConfig) {
                this.logger.warn(
                    `Loader or target config not found for split target: ${targetName}. Skipping.`
                );
                continue;
            }

            const loadResult = await loader.load(records);

            let finalResult = loadResult;
            if (loader.finalize) {
                finalResult = await loader.finalize();
            }

            loadResults.push(finalResult);
            outputs[targetName] = records;
            totalRecords += records.length;
        }

        const duration = Date.now() - startTime;
        const allSuccessful = loadResults.every((r) => r.success);
        const allErrors = loadResults.flatMap((r) => r.errors);

        return {
            success: allSuccessful,
            totalRecords,
            duration,
            outputs,
            errors: allErrors,
        };
    }

    /**
     * Validate configuration before execution
     */
    validateConfiguration(configPath: string): string[] {
        const validation = this.configManager.validateConfigFile(configPath);
        return validation.errors;
    }

    /**
     * Get flow execution order (for migration configs)
     */
    getFlowExecutionOrder(configPath: string): string[] {
        return this.configManager.listFlows(configPath);
    }

    /**
     * Test connections for a configuration
     */
    async testConnections(config: EtlConfig): Promise<Record<string, boolean>> {
        const results: Record<string, boolean> = {};

        let connections: Record<string, any> = {};

        if (isSimpleJobConfig(config)) {
            connections = config.connections || {};
        } else if (isMigrationConfig(config)) {
            connections = config.connections;
        }

        for (const [name, connectionConfig] of Object.entries(connections)) {
            try {
                const isValid = await this.connectionService.testConnection(
                    connectionConfig
                );
                results[name] = isValid;
            } catch (error) {
                this.logger.error(`Connection test failed for ${name}`, error);
                results[name] = false;
            }
        }

        return results;
    }
}
