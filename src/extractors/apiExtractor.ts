import * as jsforce from "jsforce";
import { Extractor } from "../core/interfaces";
import { DataSourceConfig } from "../config/schema";
import { ConnectionService } from "../services/connectionService";
import { Logger } from "../utils/logger";
import { CsvHelper } from "../utils/csvHelper";
import * as path from 'path';

/**
 * Simplified Salesforce API extractor for ETL v2
 * Removed complex CSV debug features and consolidated logic
 */
export class ApiExtractor implements Extractor {
    private connection: jsforce.Connection | null = null;

    constructor(
        private config: DataSourceConfig,
        private connectionService: ConnectionService,
        private connectionName: string,
        private rawConnectionConfig: any,
        private logger: Logger,
        private recordLimit?: number,
        private debugOptions?: { saveExtractorCsv?: boolean }
    ) {
        if (config.type !== "api") {
            throw new Error("ApiExtractor initialized with non-API DataSourceConfig.");
        }

        // Validate configuration
        if (this.config.query && this.config.query.trim() !== "") {
            const sanitizedQuery = this.config.query.trim().toLowerCase();
            if (!sanitizedQuery.startsWith("select")) {
                throw new Error('Custom SOQL query must start with "SELECT".');
            }
        } else {
            if (!this.config.object) {
                throw new Error('API extractor requires "object"');
            }
            if (!this.config.fields || this.config.fields.length === 0) {
                throw new Error('API extractor requires "fields"');
            }
        }
    }

    private async initializeConnection(): Promise<void> {
        if (!this.connection) {
            this.connection = await this.connectionService.getConnection(
                this.connectionName,
                this.rawConnectionConfig
            );
        }
    }

    private buildQuery(): string {
        if (this.config.query && this.config.query.trim() !== "") {
            return this.config.query.trim();
        }

        const fields = this.config.fields!.join(", ");
        const object = this.config.object!;
        let query = `SELECT ${fields} FROM ${object}`;
        
        if (this.config.whereClause) {
            query += ` WHERE ${this.config.whereClause.trim()}`;
        }
        
        return query;
    }

    async *extract(): AsyncGenerator<Record<string, any>[], void, unknown> {
        await this.initializeConnection();
        const query = this.buildQuery();
        
        this.logger.info(`Starting API extraction with query: ${query.substring(0, 200)}${query.length > 200 ? "..." : ""}`);

        const maxFetch = this.recordLimit || 200000;
        const queryStream = this.connection!.query(query);
        queryStream.run({ autoFetch: true, maxFetch });

        const batchSize = this.config.batchSize || 1000;
        let batch: Record<string, any>[] = [];
        let totalYielded = 0;

        try {
            const stream = queryStream.stream('record');

            for await (const record of stream) {
                if (record && typeof record.attributes !== "undefined") {
                    delete record.attributes;
                }
                batch.push(record as Record<string, any>);

                if (batch.length >= batchSize) {
                    await this.saveBatchIfDebug(batch);
                    yield batch;
                    totalYielded += batch.length;
                    batch = [];
                }

                if (this.recordLimit && totalYielded + batch.length >= this.recordLimit) {
                    if (batch.length > 0) {
                        const recordsToYield = batch.slice(0, this.recordLimit - totalYielded);
                        if (recordsToYield.length > 0) {
                            await this.saveBatchIfDebug(recordsToYield);
                            yield recordsToYield;
                            totalYielded += recordsToYield.length;
                        }
                    }
                    this.logger.info(`Record limit of ${this.recordLimit} reached.`);
                    break;
                }
            }

            if (batch.length > 0) {
                await this.saveBatchIfDebug(batch);
                yield batch;
                totalYielded += batch.length;
            }

            this.logger.info(`API extraction completed: ${totalYielded} total records`);

        } catch (error) {
            this.logger.error(`API extraction failed: ${error instanceof Error ? error.message : String(error)}`, error);
            throw error;
        }
    }

    async getRecordCount(): Promise<number> {
        await this.initializeConnection();

        if (this.config.query && this.config.query.trim() !== "" && !this.config.object) {
            this.logger.warn('Cannot determine record count for custom query without object specified');
            return 0;
        }

        if (this.config.object) {
            let countQuery = `SELECT COUNT() FROM ${this.config.object}`;
            if (this.config.whereClause) {
                countQuery += ` WHERE ${this.config.whereClause}`;
            }

            try {
                const result = await this.connection!.query<{ TotalSize: number }>(countQuery);
                return result.totalSize;
            } catch (error) {
                this.logger.error('Failed to get record count', error);
                throw new Error(`Failed to get record count: ${error instanceof Error ? error.message : String(error)}`);
            }
        }

        return 0;
    }

    private async saveBatchIfDebug(batch: Record<string, any>[]): Promise<void> {
        if (!this.debugOptions?.saveExtractorCsv || !batch.length) return;

        try {
            const baseName = this.config.object || 'customApiQuery';
            const filePath = path.join(process.cwd(), 'staging', `${baseName}_extracted_raw.csv`);
            await CsvHelper.writeToCsv(batch, filePath, true);
        } catch (error) {
            this.logger.warn('Failed to save debug CSV', error);
        }
    }
}
