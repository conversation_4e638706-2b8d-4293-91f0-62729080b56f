import * as fs from "fs";
import * as path from "path";
import * as glob from "glob";
import csv from "csv-parser";
import { Extractor } from "../core/interfaces";
import { DataSourceConfig } from "../config/schema";
import { Logger } from "../utils/logger";

/**
 * Enhanced CSV file extractor for ETL v2
 * Supports multiple CSV files with glob patterns and directory scanning
 * Adds file metadata for intelligent job splitting
 */
export class EnhancedCsvExtractor implements Extractor {
    private filePaths: string[] = [];
    private totalRecordCount: number = 0;

    constructor(
        private config: DataSourceConfig & {
            // New options for multiple files
            filePattern?: string; // Glob pattern like "accounts_*.csv"
            directory?: string; // Directory to scan for files
            fileList?: string[]; // Explicit list of file paths
            sortFiles?: boolean; // Sort files alphabetically (default: true)
            maxFiles?: number; // Limit number of files to process
            addFileMetadata?: boolean; // Add file source metadata to records (default: true)
        },
        private logger: Logger,
        private recordLimit?: number
    ) {
        this.initializeFilePaths();
    }

    /**
     * Initialize and validate file paths based on configuration
     */
    private initializeFilePaths(): void {
        // Method 1: Single file (existing behavior)
        if (
            this.config.filePath &&
            !this.config.filePattern &&
            !this.config.directory &&
            !this.config.fileList
        ) {
            if (!fs.existsSync(this.config.filePath)) {
                throw new Error(`CSV file not found: ${this.config.filePath}`);
            }
            this.filePaths = [this.config.filePath];
            return;
        }

        // Method 2: Explicit file list
        if (this.config.fileList && this.config.fileList.length > 0) {
            for (const filePath of this.config.fileList) {
                if (!fs.existsSync(filePath)) {
                    throw new Error(`CSV file not found: ${filePath}`);
                }
                this.filePaths.push(filePath);
            }
        }

        // Method 3: Directory scanning with optional pattern
        else if (this.config.directory) {
            const directory = this.config.directory;
            if (!fs.existsSync(directory)) {
                throw new Error(`Directory not found: ${directory}`);
            }

            const pattern = this.config.filePattern || "*.csv";
            const searchPattern = path.join(directory, pattern);

            try {
                this.filePaths = glob.sync(searchPattern, { nodir: true });
            } catch (error) {
                throw new Error(
                    `Failed to scan directory with pattern "${searchPattern}": ${
                        error instanceof Error ? error.message : String(error)
                    }`
                );
            }

            if (this.filePaths.length === 0) {
                throw new Error(
                    `No CSV files found matching pattern: ${searchPattern}`
                );
            }
        }

        // Method 4: Glob pattern from current directory
        else if (this.config.filePattern) {
            try {
                this.filePaths = glob.sync(this.config.filePattern, {
                    nodir: true,
                });
            } catch (error) {
                throw new Error(
                    `Failed to resolve file pattern "${
                        this.config.filePattern
                    }": ${
                        error instanceof Error ? error.message : String(error)
                    }`
                );
            }

            if (this.filePaths.length === 0) {
                throw new Error(
                    `No CSV files found matching pattern: ${this.config.filePattern}`
                );
            }
        } else {
            throw new Error(
                "Enhanced CSV extractor requires one of: filePath, fileList, directory, or filePattern"
            );
        }

        // Sort files alphabetically for consistent processing order
        if (this.config.sortFiles !== false) {
            this.filePaths.sort();
        }

        // Limit number of files if specified
        if (this.config.maxFiles && this.config.maxFiles > 0) {
            this.filePaths = this.filePaths.slice(0, this.config.maxFiles);
        }

        this.logger.info(
            `Enhanced CSV extractor initialized with ${this.filePaths.length} files:`,
            {
                files: this.filePaths.map((f) => path.basename(f)),
            }
        );
    }

    async *extract(): AsyncGenerator<Record<string, any>[], void, unknown> {
        const batchSize = this.config.batchSize || 1000;
        const addFileMetadata = this.config.addFileMetadata !== false; // Default to true
        let batch: Record<string, any>[] = [];
        let totalRecordCount = 0;

        this.logger.info(
            `Starting enhanced CSV extraction from ${
                this.filePaths.length
            } files${
                this.recordLimit ? ` with limit: ${this.recordLimit}` : ""
            }`
        );

        for (
            let fileIndex = 0;
            fileIndex < this.filePaths.length;
            fileIndex++
        ) {
            const filePath = this.filePaths[fileIndex];
            let fileRecordCount = 0;
            const fileName = path.basename(filePath);
            const fileSize = fs.statSync(filePath).size;

            this.logger.info(
                `Processing file ${fileIndex + 1}/${
                    this.filePaths.length
                }: ${fileName} ` +
                    `(${Math.round((fileSize / 1024 / 1024) * 100) / 100} MB)`
            );

            try {
                const stream = fs.createReadStream(filePath).pipe(csv());

                for await (const record of stream) {
                    // Check global record limit
                    if (
                        this.recordLimit &&
                        totalRecordCount >= this.recordLimit
                    ) {
                        this.logger.info(
                            `Global record limit of ${this.recordLimit} reached. Stopping CSV extraction at ${totalRecordCount} records.`
                        );
                        if (batch.length > 0) {
                            yield [...batch];
                        }
                        return;
                    }

                    // Add file metadata for job splitting (if enabled and multiple files)
                    if (addFileMetadata && this.filePaths.length > 1) {
                        record.__sourceFile = filePath;
                        record.__fileName = fileName;
                        record.__fileIndex = fileIndex;
                        record.__recordIndex = fileRecordCount;
                    }

                    batch.push(record);
                    fileRecordCount++;
                    totalRecordCount++;

                    if (batch.length >= batchSize) {
                        this.logger.debug(
                            `CSV batch extracted: ${batch.length} records (file: ${fileRecordCount}, total: ${totalRecordCount})`
                        );
                        yield [...batch];
                        batch = [];
                    }

                    // Check global limit again after incrementing
                    if (
                        this.recordLimit &&
                        totalRecordCount >= this.recordLimit
                    ) {
                        this.logger.info(
                            `Global record limit of ${this.recordLimit} reached. Stopping CSV extraction at ${totalRecordCount} records.`
                        );
                        if (batch.length > 0) {
                            yield [...batch];
                        }
                        return;
                    }
                }

                this.logger.info(
                    `Completed file ${fileName}: ${fileRecordCount} records`
                );

                // If we're maintaining file boundaries and there are records in the batch,
                // yield them before starting the next file
                if (
                    addFileMetadata &&
                    batch.length > 0 &&
                    this.filePaths.length > 1
                ) {
                    this.logger.debug(
                        `End of file batch: ${batch.length} records from ${fileName}`
                    );
                    yield [...batch];
                    batch = [];
                }
            } catch (error) {
                this.logger.error(`Error processing file ${filePath}:`, error);
                throw new Error(
                    `Failed to process CSV file ${filePath}: ${
                        error instanceof Error ? error.message : String(error)
                    }`
                );
            }
        }

        // Yield remaining records
        if (batch.length > 0) {
            this.logger.debug(
                `CSV final batch extracted: ${batch.length} records (total: ${totalRecordCount})`
            );
            yield batch;
        }

        this.totalRecordCount = totalRecordCount;
        this.logger.info(
            `Enhanced CSV extraction completed: ${totalRecordCount} total records from ${this.filePaths.length} files`
        );
    }

    async getRecordCount(): Promise<number> {
        if (this.totalRecordCount > 0) {
            return this.totalRecordCount;
        }

        // Count records across all files
        let totalCount = 0;

        this.logger.info(
            `Counting records in ${this.filePaths.length} files...`
        );

        for (const filePath of this.filePaths) {
            const fileCount = await new Promise<number>((resolve, reject) => {
                let count = 0;
                fs.createReadStream(filePath)
                    .pipe(csv())
                    .on("data", () => count++)
                    .on("end", () => resolve(count))
                    .on("error", reject);
            });
            totalCount += fileCount;
            this.logger.debug(
                `${path.basename(filePath)}: ${fileCount} records`
            );
        }

        this.totalRecordCount = totalCount;
        this.logger.info(`Total record count: ${totalCount} records`);
        return totalCount;
    }

    /**
     * Get list of files that will be processed
     */
    getFilePaths(): string[] {
        return [...this.filePaths];
    }

    /**
     * Get file processing statistics
     */
    getFileStats(): Array<{
        path: string;
        name: string;
        size: number;
        estimatedRecords?: number;
    }> {
        return this.filePaths.map((filePath) => {
            const stats = fs.statSync(filePath);
            return {
                path: filePath,
                name: path.basename(filePath),
                size: stats.size,
                estimatedRecords: Math.round(stats.size / 100), // Simple estimation
            };
        });
    }
}
