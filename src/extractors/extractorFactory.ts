import { Extractor, JobContext } from "../core/interfaces";
import { ConnectionConfig, DataSourceConfig } from "../config/schema";
import { ConnectionService } from "../services/connectionService";
import { Logger } from "../utils/logger";
import { EnhancedCsvExtractor } from "./enhancedCsvExtractor";
import { ApiExtractor } from "./apiExtractor";
import { FlowOutputExtractor } from "./flowOutputExtractor";

/**
 * Simplified extractor factory for ETL v2
 */
export class ExtractorFactory {
    constructor(
        private connectionService: ConnectionService,
        private logger: Logger
    ) {}

    /**
     * Create an extractor based on data source configuration
     */
    createExtractor(
        config: DataSourceConfig,
        connections: Record<string, ConnectionConfig>,
        context?: JobContext,
        recordLimit?: number,
        debugOptions?: { saveExtractorCsv?: boolean }
    ): Extractor {
        this.logger.debug(`Creating extractor of type: ${config.type}`);

        switch (config.type) {
            case "csv":
                return new EnhancedCsvExtractor(
                    config,
                    this.logger,
                    recordLimit
                );

            case "api":
                if (!config.connection) {
                    throw new Error(
                        'API extractor requires a "connection" name'
                    );
                }
                const connectionConfig = connections[config.connection];
                if (!connectionConfig) {
                    throw new Error(
                        `Connection configuration not found: ${config.connection}`
                    );
                }

                return new ApiExtractor(
                    config,
                    this.connectionService,
                    config.connection,
                    connectionConfig,
                    this.logger,
                    recordLimit,
                    debugOptions
                );

            case "flow-output":
                if (!context) {
                    throw new Error(
                        "Flow output extractor requires job context"
                    );
                }
                return new FlowOutputExtractor(config, context, this.logger);

            default:
                const exhaustiveCheck: never = config.type;
                throw new Error(
                    `Unsupported extractor type: ${exhaustiveCheck}`
                );
        }
    }

    /**
     * Create multiple extractors for multi-source flows
     */
    async createMultipleExtractors(
        sources: Array<{ name: string; alias?: string }>,
        dataSources: Record<string, DataSourceConfig>,
        connections: Record<string, ConnectionConfig>,
        context?: JobContext,
        recordLimit?: number,
        debugOptions?: { saveExtractorCsv?: boolean }
    ): Promise<Map<string, Extractor>> {
        const extractors = new Map<string, Extractor>();

        // Initialize connections if there are API extractors
        const hasApiExtractors = sources.some((sourceRef) => {
            const dataSourceConfig = dataSources[sourceRef.name];
            return dataSourceConfig?.type === "api";
        });

        if (hasApiExtractors) {
            await this.connectionService.initializeConnections(connections);
        }

        for (const sourceRef of sources) {
            const dataSourceConfig = dataSources[sourceRef.name];
            if (!dataSourceConfig) {
                throw new Error(
                    `Data source configuration not found: ${sourceRef.name}`
                );
            }

            const extractor = this.createExtractor(
                dataSourceConfig,
                connections,
                context,
                recordLimit,
                debugOptions
            );

            const key = sourceRef.alias || sourceRef.name;
            extractors.set(key, extractor);
        }

        return extractors;
    }
}
