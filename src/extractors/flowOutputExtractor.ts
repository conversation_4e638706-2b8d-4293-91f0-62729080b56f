import { Extractor } from '../core/interfaces';
import { DataSourceConfig } from '../config/schema';
import { JobContext } from '../core/interfaces';
import { Logger } from '../utils/logger';

/**
 * Flow output extractor for cross-flow data linking
 */
export class FlowOutputExtractor implements Extractor {
    constructor(
        private config: DataSourceConfig,
        private context: JobContext,
        private logger: Logger
    ) {
        if (!config.sourceFlow || !config.sourceTarget) {
            throw new Error('Flow output extractor requires sourceFlow and sourceTarget in configuration');
        }
    }

    async *extract(): AsyncGenerator<Record<string, any>[], void, unknown> {
        const sourceKey = `${this.config.sourceFlow}-${this.config.sourceTarget}`;
        const data = this.context.flowOutputs.get(sourceKey);

        if (!data || data.length === 0) {
            this.logger.warn(`No data found for flow output: ${sourceKey}`);
            return;
        }

        this.logger.info(`Starting flow output extraction: ${data.length} records from ${sourceKey}`);

        const batchSize = this.config.batchSize || 1000;

        // Process in batches
        for (let i = 0; i < data.length; i += batchSize) {
            const batch = data.slice(i, i + batchSize);
            this.logger.debug(`Flow output batch extracted: ${batch.length} records`);
            yield batch;
        }

        this.logger.info(`Flow output extraction completed: ${data.length} total records`);
    }

    async getRecordCount(): Promise<number> {
        const sourceKey = `${this.config.sourceFlow}-${this.config.sourceTarget}`;
        const data = this.context.flowOutputs.get(sourceKey);
        return data ? data.length : 0;
    }
}
