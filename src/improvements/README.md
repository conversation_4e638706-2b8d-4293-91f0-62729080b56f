# ETL v2 - Large Volume Handling Improvements

## Overview
This document outlines the improvements needed to handle large volume data processing (500k+ records) without memory overflow issues.

## Current Issues

1. **Memory Accumulation**
   - The orchestrator accumulates all records in memory before transformation
   - BulkLoader stores all records until `finalize()` is called
   - Transformers expect all records at once

2. **No Streaming for Large CSVs**
   - CSV helper loads entire files into memory
   - No streaming write capabilities for result CSVs

3. **Limited Batch Control**
   - No automatic splitting of large datasets into multiple bulk jobs
   - Fixed batch sizes regardless of data volume

## Implemented Improvements

### 1. StreamingBulkLoader
- Writes records to temporary CSV files instead of memory accumulation
- Automatically splits data into multiple bulk jobs when reaching 150k records (Salesforce recommendation)
- Saves audit trail of uploaded CSVs and job results with timestamps
- Efficient streaming for result CSV writing

### 2. StreamingCsvHelper
- Provides streaming write capabilities for large CSVs
- `writeRecordsStreaming()` - Writes large datasets in batches
- `createCsvTransformStream()` - Transform stream for on-the-fly CSV conversion
- `streamToCsv()` - Direct streaming from async generators to CSV files
- `appendToCsv()` - Efficient append operations

### 3. BatchTransformer Interface
- New interface for transformers that can process data in batches
- `StreamingTransformerWrapper` wraps existing transformers for backward compatibility
- Supports `initialize()` and `finalize()` methods for stateful transformations

### 4. StreamingOrchestrator
- Extends the base orchestrator with streaming capabilities
- Processes data in batches through the entire pipeline
- Saves transformed CSVs for auditing when debug mode is enabled
- Automatically detects large CSV files and adjusts batch sizes

## Integration Guide

### 1. Update the BulkLoader in loaderFactory.ts

```typescript
import { StreamingBulkLoader } from '../improvements/streamingBulkLoader';

private createBulkLoader(
    config: TargetConfig,
    connections: Record<string, ConnectionConfig>
): BulkLoader {
    const connectionConfig = connections[config.connection];
    if (!connectionConfig) {
        throw new Error(`Connection configuration not found: ${config.connection}`);
    }

    // Use StreamingBulkLoader for better memory management
    return new StreamingBulkLoader(
        config,
        this.connectionService,
        config.connection,
        this.logger,
        config.maxRecordsPerJob || 150000
    );
}
```

### 2. Update Transformers to Support Batch Processing

For new transformers, implement the BatchTransformer interface:

```typescript
import { BaseBatchTransformer } from '../improvements/batchTransformer';

export class StreamingSimpleTransformer extends BaseBatchTransformer {
    async transformBatch(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        // Process records in this batch
        return records.map(record => this.transformRecord(record));
    }
    
    private transformRecord(record: Record<string, any>): Record<string, any> {
        // Apply mappings
    }
}
```

### 3. Update the Main Index to Use Streaming Orchestrator

```typescript
import { StreamingOrchestrator } from './improvements/streamingOrchestrator';

export function createEtlOrchestrator(logLevel?: string): EtlOrchestrator {
    const logger = new Logger(logLevel);
    const configManager = new ConfigManager(logger);
    const connectionService = new ConnectionService(logger);
    const extractorFactory = new ExtractorFactory(connectionService, logger);
    const transformerFactory = new TransformerFactory(logger);
    const loaderFactory = new LoaderFactory(connectionService, logger);

    // Use StreamingOrchestrator for better memory management
    return new StreamingOrchestrator(
        configManager,
        connectionService,
        extractorFactory,
        transformerFactory,
        loaderFactory,
        logger
    );
}
```

## Configuration Updates

Add these new options to your configuration:

```yaml
# For targets
target:
  name: accountLoad
  object: Account
  connection: salesforce
  operation: upsert
  loadStrategy: bulk2
  externalId: External_Id__c
  maxRecordsPerJob: 150000  # Split into multiple jobs
  bulkPollIntervalMs: 10000
  bulkPollTimeoutMs: 1800000  # 30 minutes for large jobs

# For sources
source:
  type: csv
  filePath: large_accounts.csv
  batchSize: 5000  # Smaller batches for memory efficiency
  
# For debug
debug:
  saveTransformerCsv: true  # Save transformed data for auditing
  saveExtractorCsv: true    # Save extracted data
```

## Best Practices for Large Volume Processing

1. **Batch Sizes**
   - Use smaller batch sizes (5k-10k) for extraction
   - Keep transformer batch sizes under 50k records
   - Bulk API jobs should not exceed 150k records

2. **File Organization**
   - Split very large CSVs (>500k records) into multiple files
   - Use `splitFilesIntoJobs: true` for parallel processing
   - Organize files by date or batch for easier tracking

3. **Memory Management**
   - Monitor Node.js heap usage: `node --max-old-space-size=4096 etl-cli`
   - Use streaming transformers for simple mappings
   - Avoid loading entire datasets for joins (use lookup tables)

4. **Error Handling**
   - Each bulk job saves its own error CSV
   - Failed records are tracked with their source job ID
   - Use `continueOnError: true` for resilient processing

5. **Performance Optimization**
   - Process files in parallel when possible
   - Use indexed lookups for join operations
   - Consider pre-sorting data for better join performance

## Monitoring and Auditing

The improved system creates comprehensive audit trails:

```
staging/
├── audit/
│   ├── [jobId]/
│   │   ├── uploaded_Account_2024-01-15T10-30-00.csv
│   │   └── job_summary_2024-01-15T10-30-00.json
├── transformed/
│   ├── accountFlow_transformed_1705321800000.csv
├── job_results/
│   ├── [jobId]/
│   │   ├── successful_records_2024-01-15T10-30-00.csv
│   │   ├── failed_records_2024-01-15T10-30-00.csv
│   │   └── unprocessed_records_2024-01-15T10-30-00.csv
```

## Testing with Large Volumes

Example command for processing 500k records:

```bash
# Set higher memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Run with batch processing
etl-cli run config/large_volume_migration.yml --log-level debug

# Monitor memory usage
etl-cli run config/large_volume_migration.yml --log-level debug --monitor-memory
```

## Future Enhancements

1. **Parallel Processing**
   - Multi-threaded transformation using worker threads
   - Parallel bulk job submission

2. **Advanced Monitoring**
   - Real-time memory usage tracking
   - Progress bars for long-running jobs
   - ETA calculations based on processing speed

3. **Resumable Jobs**
   - Checkpoint system for long-running migrations
   - Ability to resume from last successful batch

4. **Data Validation**
   - Pre-flight checks for data quality
   - Automatic data type validation
   - Referential integrity checks
