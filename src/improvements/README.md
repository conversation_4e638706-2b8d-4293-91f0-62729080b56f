# ETL v2 - Enhanced Streaming Architecture with SOLID Principles

## Overview

This directory contains the enhanced ETL architecture with streaming support, featuring the **AugmentContextEngine** for memory-efficient processing of large datasets (500k+ records). The architecture follows **SOLID principles** and **DRY (Don't Repeat Yourself)** for maintainable, extensible code.

## 🎯 SOLID Principles Implementation

### **S** - Single Responsibility Principle ✅

Each class has one reason to change:

-   `AugmentContextEngine`: Context management only
-   `StreamingCsvExtractor`: CSV extraction only
-   `FlowExecutionStrategy`: Flow execution logic only
-   `StreamingComponentFactory`: Component creation only

### **O** - Open/Closed Principle ✅

Components are open for extension, closed for modification:

-   New execution strategies can be added without changing existing code
-   New extractor types can be added via factory pattern
-   New transformer types can be wrapped for batch processing

### **L** - Liskov Substitution Principle ✅

Derived classes are substitutable for their base classes:

-   All execution strategies implement `FlowExecutionStrategy`
-   All context managers implement `StreamingContextManager`
-   All extractors implement `StreamingExtractor<T>`

### **I** - Interface Segregation Principle ✅

Clients depend only on interfaces they use:

-   `MemoryMonitor` for memory tracking only
-   `ContextPersistence` for persistence operations only
-   `ContextSnapshots` for snapshot functionality only

### **D** - Dependency Inversion Principle ✅

High-level modules depend on abstractions:

-   `EnhancedOrchestrator` depends on interfaces, not concrete classes
-   Components are injected via constructor (Dependency Injection)
-   Factory pattern provides abstraction over object creation

## 🚀 Key Features

### AugmentContextEngine

The core innovation that enables stream processing with intelligent memory management:

-   **Context Management**: Tracks job state, flow outputs, and global variables
-   **Memory Monitoring**: Automatic persistence when memory thresholds are exceeded
-   **Streaming Context**: Optimized for batch processing with configurable sizes
-   **Snapshots**: Point-in-time state capture for debugging and recovery

### Enhanced Orchestrator

-   **Streaming Flow Execution**: Processes data in configurable batches
-   **Memory-Efficient Processing**: Automatic context cleanup and persistence
-   **Batch Transformer Support**: Wraps existing transformers for streaming
-   **Real-time Monitoring**: Memory usage tracking and reporting

### Streaming Components

1. **StreamingCsvExtractor**: Memory-efficient CSV processing for large files
2. **StreamingCsvHelper**: Batch writing and streaming utilities
3. **StreamingBulkLoader**: Automatic job splitting for Salesforce limits
4. **BatchTransformer**: Interface for streaming-aware transformations

## 🏗️ Architecture

### Core Components

#### AugmentContextEngine

```typescript
// Create streaming context
const context = contextEngine.createStreamContext("job-name", {
    batchSize: 10000,
    maxMemoryRecords: 100000,
    enablePersistence: true,
});

// Update flow outputs with automatic persistence
await contextEngine.updateFlowOutput("job-name", "flow-name", records);

// Monitor memory usage
const stats = contextEngine.getMemoryStats("job-name");
console.log(`Memory: ${stats.memoryUsageMB}MB, Records: ${stats.totalRecords}`);
```

#### Enhanced Orchestrator

```typescript
import { createEnhancedEtlOrchestrator } from "./improvements";

const orchestrator = createEnhancedEtlOrchestrator("info", {
    enablePersistence: true,
    maxMemoryRecords: 50000,
});

const result = await orchestrator.executeFromFile("config.yml");
```

### Streaming Flow Types

#### 1. Simple Flows

-   **Memory Usage**: Minimal - processes in batches
-   **Best For**: Large CSV transformations, data cleaning
-   **Features**: Automatic batch sizing, memory monitoring

#### 2. Join Flows

-   **Memory Usage**: Moderate - reference data in memory, primary data streamed
-   **Best For**: Enriching large datasets with lookup data
-   **Features**: Reference data limits, streaming primary processing

#### 3. Split Flows

-   **Memory Usage**: Low - routes records to multiple targets
-   **Best For**: Data distribution, conditional routing
-   **Features**: Target-specific loading, parallel processing

#### 4. Lookup Flows

-   **Memory Usage**: Moderate - lookup tables in memory
-   **Best For**: Data enrichment, validation
-   **Features**: Configurable lookup limits, streaming lookups

#### 5. Aggregation Flows

-   **Memory Usage**: High - requires data accumulation
-   **Best For**: Reporting, data summarization
-   **Features**: Chunked processing, temporary file storage

## Integration Guide

### 1. Update the BulkLoader in loaderFactory.ts

```typescript
import { StreamingBulkLoader } from '../improvements/streamingBulkLoader';

private createBulkLoader(
    config: TargetConfig,
    connections: Record<string, ConnectionConfig>
): BulkLoader {
    const connectionConfig = connections[config.connection];
    if (!connectionConfig) {
        throw new Error(`Connection configuration not found: ${config.connection}`);
    }

    // Use StreamingBulkLoader for better memory management
    return new StreamingBulkLoader(
        config,
        this.connectionService,
        config.connection,
        this.logger,
        config.maxRecordsPerJob || 150000
    );
}
```

### 2. Update Transformers to Support Batch Processing

For new transformers, implement the BatchTransformer interface:

```typescript
import { BaseBatchTransformer } from "../improvements/batchTransformer";

export class StreamingSimpleTransformer extends BaseBatchTransformer {
    async transformBatch(
        records: Record<string, any>[]
    ): Promise<Record<string, any>[]> {
        // Process records in this batch
        return records.map((record) => this.transformRecord(record));
    }

    private transformRecord(record: Record<string, any>): Record<string, any> {
        // Apply mappings
    }
}
```

### 3. Update the Main Index to Use Streaming Orchestrator

```typescript
import { StreamingOrchestrator } from "./improvements/streamingOrchestrator";

export function createEtlOrchestrator(logLevel?: string): EtlOrchestrator {
    const logger = new Logger(logLevel);
    const configManager = new ConfigManager(logger);
    const connectionService = new ConnectionService(logger);
    const extractorFactory = new ExtractorFactory(connectionService, logger);
    const transformerFactory = new TransformerFactory(logger);
    const loaderFactory = new LoaderFactory(connectionService, logger);

    // Use StreamingOrchestrator for better memory management
    return new StreamingOrchestrator(
        configManager,
        connectionService,
        extractorFactory,
        transformerFactory,
        loaderFactory,
        logger
    );
}
```

## Configuration Updates

Add these new options to your configuration:

```yaml
# For targets
target:
    name: accountLoad
    object: Account
    connection: salesforce
    operation: upsert
    loadStrategy: bulk2
    externalId: External_Id__c
    maxRecordsPerJob: 150000 # Split into multiple jobs
    bulkPollIntervalMs: 10000
    bulkPollTimeoutMs: 1800000 # 30 minutes for large jobs

# For sources
source:
    type: csv
    filePath: large_accounts.csv
    batchSize: 5000 # Smaller batches for memory efficiency

# For debug
debug:
    saveTransformerCsv: true # Save transformed data for auditing
    saveExtractorCsv: true # Save extracted data
```

## Best Practices for Large Volume Processing

1. **Batch Sizes**

    - Use smaller batch sizes (5k-10k) for extraction
    - Keep transformer batch sizes under 50k records
    - Bulk API jobs should not exceed 150k records

2. **File Organization**

    - Split very large CSVs (>500k records) into multiple files
    - Use `splitFilesIntoJobs: true` for parallel processing
    - Organize files by date or batch for easier tracking

3. **Memory Management**

    - Monitor Node.js heap usage: `node --max-old-space-size=4096 etl-cli`
    - Use streaming transformers for simple mappings
    - Avoid loading entire datasets for joins (use lookup tables)

4. **Error Handling**

    - Each bulk job saves its own error CSV
    - Failed records are tracked with their source job ID
    - Use `continueOnError: true` for resilient processing

5. **Performance Optimization**
    - Process files in parallel when possible
    - Use indexed lookups for join operations
    - Consider pre-sorting data for better join performance

## Monitoring and Auditing

The improved system creates comprehensive audit trails:

```
staging/
├── audit/
│   ├── [jobId]/
│   │   ├── uploaded_Account_2024-01-15T10-30-00.csv
│   │   └── job_summary_2024-01-15T10-30-00.json
├── transformed/
│   ├── accountFlow_transformed_1705321800000.csv
├── job_results/
│   ├── [jobId]/
│   │   ├── successful_records_2024-01-15T10-30-00.csv
│   │   ├── failed_records_2024-01-15T10-30-00.csv
│   │   └── unprocessed_records_2024-01-15T10-30-00.csv
```

## Testing with Large Volumes

Example command for processing 500k records:

```bash
# Set higher memory limit
export NODE_OPTIONS="--max-old-space-size=4096"

# Run with batch processing
etl-cli run config/large_volume_migration.yml --log-level debug

# Monitor memory usage
etl-cli run config/large_volume_migration.yml --log-level debug --monitor-memory
```

## Future Enhancements

1. **Parallel Processing**

    - Multi-threaded transformation using worker threads
    - Parallel bulk job submission

2. **Advanced Monitoring**

    - Real-time memory usage tracking
    - Progress bars for long-running jobs
    - ETA calculations based on processing speed

3. **Resumable Jobs**

    - Checkpoint system for long-running migrations
    - Ability to resume from last successful batch

4. **Data Validation**
    - Pre-flight checks for data quality
    - Automatic data type validation
    - Referential integrity checks
