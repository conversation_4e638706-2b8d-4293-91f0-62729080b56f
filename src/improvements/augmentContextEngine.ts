import { JobContext } from "../core/interfaces";
import { Logger } from "../utils/logger";
import {
    StreamingContextManager,
    ContextSnapshot,
    StreamContext,
    StreamContextOptions,
    MemoryStats,
} from "./interfaces";
import {
    BaseStreamingComponent,
    StreamingUtils,
} from "./baseStreamingComponent";
import * as fs from "fs";
import * as path from "path";

/**
 * AugmentContextEngine - Advanced context management for ETL operations
 * Follows Single Responsibility Principle: Context management only
 * Implements StreamingContextManager interface
 */
export class AugmentContextEngine
    extends BaseStreamingComponent
    implements StreamingContextManager
{
    private contexts: Map<string, StreamContext> = new Map();
    private snapshots: Map<string, ContextSnapshot[]> = new Map();
    private tempDirectory: string;
    private maxSnapshotsPerJob: number = 10;
    private memoryThreshold: number = 100000; // Max records in memory per context
    private enablePersistence: boolean;

    constructor(
        logger: Logger,
        tempDir?: string,
        enablePersistence: boolean = true
    ) {
        super(logger, {
            enablePersistence,
            tempDirectory:
                tempDir || path.join(process.cwd(), "staging", "context"),
            maxMemoryRecords: 100000,
            batchSize: 10000,
        });

        this.tempDirectory = this.config.tempDirectory;
        this.enablePersistence = this.config.enablePersistence;
        this.ensureTempDirectory();
    }

    /**
     * Create or update a streaming context for a job
     */
    createStreamContext(
        jobName: string,
        options: StreamContextOptions = {}
    ): StreamContext {
        const context: StreamContext = {
            jobName,
            flowOutputs: new Map(),
            globalVariables: options.globalVariables || {},
            recordLimit: options.recordLimit,
            batchSize: options.batchSize || 10000,
            maxMemoryRecords: options.maxMemoryRecords || this.memoryThreshold,
            tempDirectory: this.tempDirectory,
            enablePersistence: this.enablePersistence,
        };

        this.contexts.set(jobName, context);
        this.logger.debug(`Created stream context for job: ${jobName}`);

        return context;
    }

    /**
     * Update flow outputs in streaming fashion
     */
    async updateFlowOutput(
        jobName: string,
        flowName: string,
        records: Record<string, any>[],
        append: boolean = true
    ): Promise<void> {
        const context = this.contexts.get(jobName);
        if (!context) {
            throw new Error(`Context not found for job: ${jobName}`);
        }

        const currentRecords = context.flowOutputs.get(flowName) || [];
        const totalRecords = append ? [...currentRecords, ...records] : records;

        // Check memory threshold
        if (
            totalRecords.length > context.maxMemoryRecords &&
            context.enablePersistence
        ) {
            await this.persistFlowOutput(jobName, flowName, totalRecords);
            context.flowOutputs.set(flowName, []); // Clear from memory
            this.logger.debug(
                `Persisted ${totalRecords.length} records for ${flowName} to disk`
            );
        } else {
            context.flowOutputs.set(flowName, totalRecords);
        }
    }

    /**
     * Get flow output with automatic loading from persistence if needed
     */
    async getFlowOutput(
        jobName: string,
        flowName: string
    ): Promise<Record<string, any>[]> {
        const context = this.contexts.get(jobName);
        if (!context) {
            throw new Error(`Context not found for job: ${jobName}`);
        }

        const memoryRecords = context.flowOutputs.get(flowName) || [];

        // Check if we have persisted data
        if (context.enablePersistence) {
            const persistedRecords = await this.loadPersistedFlowOutput(
                jobName,
                flowName
            );
            return [...persistedRecords, ...memoryRecords];
        }

        return memoryRecords;
    }

    /**
     * Create a snapshot of the current context state
     */
    createSnapshot(jobName: string): ContextSnapshot {
        const context = this.contexts.get(jobName);
        if (!context) {
            throw new Error(`Context not found for job: ${jobName}`);
        }

        const snapshot: ContextSnapshot = {
            jobName,
            flowOutputs: this.convertMapToRecord(context.flowOutputs),
            globalVariables: { ...context.globalVariables },
            timestamp: Date.now(),
            recordCounts: this.getRecordCounts(context.flowOutputs),
        };

        // Store snapshot
        const jobSnapshots = this.snapshots.get(jobName) || [];
        jobSnapshots.push(snapshot);

        // Keep only recent snapshots
        if (jobSnapshots.length > this.maxSnapshotsPerJob) {
            jobSnapshots.shift();
        }

        this.snapshots.set(jobName, jobSnapshots);

        return snapshot;
    }

    /**
     * Get snapshots for a job
     */
    getSnapshots(jobName: string): ContextSnapshot[] {
        return this.snapshots.get(jobName) || [];
    }

    /**
     * Get memory usage statistics for a job
     */
    getMemoryStats(jobName: string): MemoryStats {
        const context = this.contexts.get(jobName);
        if (!context) {
            return { totalRecords: 0, flowCounts: {}, memoryUsageMB: 0 };
        }

        const flowCounts = this.getRecordCounts(context.flowOutputs);
        const totalRecords = Object.values(flowCounts).reduce(
            (sum, count) => sum + count,
            0
        );

        // Rough memory estimation (assuming ~1KB per record)
        const memoryUsageMB = (totalRecords * 1024) / (1024 * 1024);

        return {
            totalRecords,
            flowCounts,
            memoryUsageMB,
        };
    }

    /**
     * Clean up context and temporary files for a specific job
     */
    async cleanupJob(jobName: string): Promise<void> {
        this.contexts.delete(jobName);
        this.snapshots.delete(jobName);

        if (this.enablePersistence) {
            const jobTempDir = path.join(this.tempDirectory, jobName);
            if (fs.existsSync(jobTempDir)) {
                await this.removeDirectory(jobTempDir);
                this.logger.debug(
                    `Cleaned up temporary files for job: ${jobName}`
                );
            }
        }
    }

    /**
     * Clean up all contexts and temporary files (override base class method)
     */
    async cleanup(): Promise<void> {
        const activeJobs = Array.from(this.contexts.keys());
        for (const jobName of activeJobs) {
            await this.cleanupJob(jobName);
        }
        await super.cleanup();
    }

    /**
     * Get all active contexts
     */
    getActiveContexts(): string[] {
        return Array.from(this.contexts.keys());
    }

    // Private helper methods
    private ensureTempDirectory(): void {
        if (!fs.existsSync(this.tempDirectory)) {
            fs.mkdirSync(this.tempDirectory, { recursive: true });
        }
    }

    /**
     * Persist flow output to disk (public for interface compliance)
     */
    async persistFlowOutput(
        jobName: string,
        flowName: string,
        records: Record<string, any>[]
    ): Promise<void> {
        const jobDir = path.join(this.tempDirectory, jobName);
        if (!fs.existsSync(jobDir)) {
            fs.mkdirSync(jobDir, { recursive: true });
        }

        const filePath = path.join(jobDir, `${flowName}.json`);
        await fs.promises.writeFile(filePath, JSON.stringify(records, null, 2));
    }

    /**
     * Load persisted flow output from disk (public for interface compliance)
     */
    async loadPersistedFlowOutput(
        jobName: string,
        flowName: string
    ): Promise<Record<string, any>[]> {
        const filePath = path.join(
            this.tempDirectory,
            jobName,
            `${flowName}.json`
        );

        if (!fs.existsSync(filePath)) {
            return [];
        }

        try {
            const data = await fs.promises.readFile(filePath, "utf-8");
            return JSON.parse(data);
        } catch (error) {
            this.logger.warn(
                `Failed to load persisted data for ${flowName}: ${error}`
            );
            return [];
        }
    }

    private convertMapToRecord(map: Map<string, any>): Record<string, any> {
        const record: Record<string, any> = {};
        for (const [key, value] of map.entries()) {
            record[key] = value;
        }
        return record;
    }

    private getRecordCounts(
        flowOutputs: Map<string, any[]>
    ): Record<string, number> {
        const counts: Record<string, number> = {};
        for (const [flowName, records] of flowOutputs.entries()) {
            counts[flowName] = records.length;
        }
        return counts;
    }

    private async removeDirectory(dirPath: string): Promise<void> {
        if (fs.existsSync(dirPath)) {
            const files = await fs.promises.readdir(dirPath);
            for (const file of files) {
                const filePath = path.join(dirPath, file);
                const stat = await fs.promises.stat(filePath);
                if (stat.isDirectory()) {
                    await this.removeDirectory(filePath);
                } else {
                    await fs.promises.unlink(filePath);
                }
            }
            await fs.promises.rmdir(dirPath);
        }
    }
}
