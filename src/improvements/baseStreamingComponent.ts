import { Logger } from '../utils/logger';
import { 
    StreamingEvent, 
    EventEmitter, 
    ProcessingMetrics, 
    StreamingConfiguration,
    ErrorHandler,
    ErrorContext
} from './interfaces';

/**
 * Base class for all streaming components
 * Follows Single Responsibility Principle: Common streaming functionality
 * Follows Open/Closed Principle: Extensible without modification
 */
export abstract class BaseStreamingComponent implements EventEmitter {
    protected readonly logger: Logger;
    protected readonly config: StreamingConfiguration;
    protected readonly metrics: ProcessingMetrics;
    private eventHandlers: Map<string, ((event: StreamingEvent) => void)[]> = new Map();

    constructor(
        logger: Logger,
        config: Partial<StreamingConfiguration> = {}
    ) {
        this.logger = logger;
        this.config = this.createDefaultConfig(config);
        this.metrics = this.initializeMetrics();
    }

    /**
     * Template method pattern for processing
     * Defines the skeleton of the algorithm
     */
    protected async processWithErrorHandling<T>(
        operation: () => Promise<T>,
        context: Partial<ErrorContext>
    ): Promise<T> {
        try {
            return await operation();
        } catch (error) {
            await this.handleError(error as Error, context);
            throw error;
        }
    }

    /**
     * Event emission following Observer pattern
     */
    emit(event: StreamingEvent): void {
        const handlers = this.eventHandlers.get(event.type) || [];
        handlers.forEach(handler => {
            try {
                handler(event);
            } catch (error) {
                this.logger.error(`Error in event handler for ${event.type}:`, error);
            }
        });
    }

    on(eventType: string, handler: (event: StreamingEvent) => void): void {
        if (!this.eventHandlers.has(eventType)) {
            this.eventHandlers.set(eventType, []);
        }
        this.eventHandlers.get(eventType)!.push(handler);
    }

    off(eventType: string, handler: (event: StreamingEvent) => void): void {
        const handlers = this.eventHandlers.get(eventType);
        if (handlers) {
            const index = handlers.indexOf(handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
    }

    /**
     * Create event with common properties
     */
    protected createEvent(type: string, data: any, jobName: string = 'unknown'): StreamingEvent {
        return {
            type,
            timestamp: Date.now(),
            jobName,
            data
        };
    }

    /**
     * Get current processing metrics
     */
    getMetrics(): ProcessingMetrics {
        return { ...this.metrics };
    }

    /**
     * Update metrics (protected to allow subclasses to modify)
     */
    protected updateMetrics(updates: Partial<ProcessingMetrics>): void {
        Object.assign(this.metrics as any, updates);
    }

    /**
     * Default error handling - can be overridden by subclasses
     */
    protected async handleError(error: Error, context: Partial<ErrorContext>): Promise<void> {
        this.logger.error(`Error in ${this.constructor.name}:`, error);
        
        const errorEvent = this.createEvent('error', {
            error: error.message,
            context,
            component: this.constructor.name
        }, context.jobName);
        
        this.emit(errorEvent);
    }

    /**
     * Validate configuration - template method
     */
    protected validateConfig(config: Partial<StreamingConfiguration>): void {
        if (config.batchSize && config.batchSize <= 0) {
            throw new Error('Batch size must be positive');
        }
        if (config.maxMemoryRecords && config.maxMemoryRecords <= 0) {
            throw new Error('Max memory records must be positive');
        }
    }

    /**
     * Create default configuration with validation
     */
    private createDefaultConfig(config: Partial<StreamingConfiguration>): StreamingConfiguration {
        this.validateConfig(config);
        
        return {
            batchSize: config.batchSize || 10000,
            maxMemoryRecords: config.maxMemoryRecords || 100000,
            enablePersistence: config.enablePersistence ?? true,
            tempDirectory: config.tempDirectory || './temp'
        };
    }

    /**
     * Initialize metrics
     */
    private initializeMetrics(): ProcessingMetrics {
        return {
            startTime: Date.now(),
            recordsProcessed: 0,
            batchesProcessed: 0,
            errorsEncountered: 0
        };
    }

    /**
     * Cleanup resources - should be called when component is no longer needed
     */
    async cleanup(): Promise<void> {
        this.eventHandlers.clear();
        this.logger.debug(`${this.constructor.name} cleaned up`);
    }
}

/**
 * Mixin for retry functionality
 * Follows Composition over Inheritance principle
 */
export class RetryMixin {
    static withRetry<T extends new (...args: any[]) => BaseStreamingComponent>(
        Base: T,
        maxRetries: number = 3,
        baseDelay: number = 1000
    ) {
        return class extends Base implements ErrorHandler {
            private retryAttempts: Map<string, number> = new Map();

            async handleError(error: Error, context: ErrorContext): Promise<void> {
                await super.handleError(error, context);
                
                if (this.shouldRetry(error)) {
                    const key = `${context.jobName}-${context.flowName}-${context.batchNumber}`;
                    const attempts = this.retryAttempts.get(key) || 0;
                    
                    if (attempts < maxRetries) {
                        this.retryAttempts.set(key, attempts + 1);
                        const delay = this.getRetryDelay(attempts + 1);
                        
                        this.logger.warn(`Retrying operation after ${delay}ms (attempt ${attempts + 1}/${maxRetries})`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        return;
                    }
                }
                
                throw error;
            }

            shouldRetry(error: Error): boolean {
                // Retry on network errors, timeouts, etc.
                return error.message.includes('timeout') || 
                       error.message.includes('network') ||
                       error.message.includes('ECONNRESET');
            }

            getRetryDelay(attemptNumber: number): number {
                // Exponential backoff with jitter
                const delay = baseDelay * Math.pow(2, attemptNumber - 1);
                const jitter = Math.random() * 0.1 * delay;
                return Math.floor(delay + jitter);
            }
        };
    }
}

/**
 * Utility class for common streaming operations
 * Follows Single Responsibility Principle
 */
export class StreamingUtils {
    /**
     * Chunk array into smaller arrays
     */
    static chunk<T>(array: T[], size: number): T[][] {
        const chunks: T[][] = [];
        for (let i = 0; i < array.length; i += size) {
            chunks.push(array.slice(i, i + size));
        }
        return chunks;
    }

    /**
     * Calculate memory usage estimation
     */
    static estimateMemoryUsage(records: Record<string, any>[]): number {
        if (records.length === 0) return 0;
        
        // Rough estimation: JSON.stringify one record and multiply
        const sampleSize = Math.min(10, records.length);
        const sample = records.slice(0, sampleSize);
        const avgSize = sample.reduce((sum, record) => {
            return sum + JSON.stringify(record).length;
        }, 0) / sampleSize;
        
        return (avgSize * records.length) / (1024 * 1024); // Convert to MB
    }

    /**
     * Validate batch size constraints
     */
    static validateBatchSize(batchSize: number, maxSize: number = 200000): void {
        if (batchSize <= 0) {
            throw new Error('Batch size must be positive');
        }
        if (batchSize > maxSize) {
            throw new Error(`Batch size ${batchSize} exceeds maximum ${maxSize}`);
        }
    }

    /**
     * Create safe filename from job/flow names
     */
    static createSafeFilename(jobName: string, flowName: string, extension: string = 'json'): string {
        const safe = (str: string) => str.replace(/[^a-zA-Z0-9-_]/g, '_');
        const timestamp = Date.now();
        return `${safe(jobName)}_${safe(flowName)}_${timestamp}.${extension}`;
    }

    /**
     * Deep clone object (for immutability)
     */
    static deepClone<T>(obj: T): T {
        return JSON.parse(JSON.stringify(obj));
    }

    /**
     * Merge configurations with precedence
     */
    static mergeConfigs<T extends Record<string, any>>(
        defaultConfig: T,
        userConfig: Partial<T>
    ): T {
        return { ...defaultConfig, ...userConfig };
    }
}
