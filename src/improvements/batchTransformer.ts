import { Transformer, JobContext } from '../core/interfaces';
import { TransformConfig } from '../config/schema';
import { Logger } from '../utils/logger';

/**
 * Batch transformer interface that processes records in chunks
 * Prevents memory overflow for large datasets
 */
export interface BatchTransformer extends Transformer {
    /**
     * Transform a batch of records
     */
    transformBatch(records: Record<string, any>[]): Promise<Record<string, any>[]>;
    
    /**
     * Initialize transformer state (called once before processing)
     */
    initialize?(): Promise<void>;
    
    /**
     * Finalize transformer (called once after all batches)
     */
    finalize?(): Promise<Record<string, any>[]>;
}

/**
 * Base class for batch transformers
 */
export abstract class BaseBatchTransformer implements BatchTransformer {
    protected initialized = false;

    constructor(
        protected config: TransformConfig,
        protected context?: JobContext,
        protected logger?: Logger
    ) {}

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        // For backward compatibility, process all records at once
        if (!this.initialized && this.initialize) {
            await this.initialize();
            this.initialized = true;
        }
        
        const result = await this.transformBatch(records);
        
        if (this.finalize) {
            const finalRecords = await this.finalize();
            return [...result, ...finalRecords];
        }
        
        return result;
    }

    abstract transformBatch(records: Record<string, any>[]): Promise<Record<string, any>[]>;
}

/**
 * Wrapper to make existing transformers work with streaming
 */
export class StreamingTransformerWrapper implements BatchTransformer {
    private buffer: Record<string, any>[] = [];
    private readonly maxBufferSize: number;

    constructor(
        private wrappedTransformer: Transformer,
        maxBufferSize: number = 50000
    ) {
        this.maxBufferSize = maxBufferSize;
    }

    async initialize(): Promise<void> {
        this.buffer = [];
    }

    async transformBatch(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        // For transformers that need all records (like aggregation), buffer them
        this.buffer.push(...records);
        
        // If buffer is getting too large, process a chunk
        if (this.buffer.length >= this.maxBufferSize) {
            const chunk = this.buffer.splice(0, this.maxBufferSize);
            return this.wrappedTransformer.transform(chunk);
        }
        
        return [];
    }

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        return this.transformBatch(records);
    }

    async finalize(): Promise<Record<string, any>[]> {
        // Process any remaining buffered records
        if (this.buffer.length > 0) {
            return this.wrappedTransformer.transform(this.buffer);
        }
        return [];
    }
}
