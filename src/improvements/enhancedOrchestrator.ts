import { EtlOrchestrator } from "../core/orchestrator";
import {
    FlowConfig,
    MigrationConfig,
    TargetConfig,
    isJoinTransform,
    isSplitTransform,
    isLookupTransform,
    isAggregationTransform,
} from "../config/schema";
import {
    Extractor,
    Transformer,
    Loader,
    FlowResult,
    JobContext,
} from "../core/interfaces";
import { StreamingCsvHelper } from "./streamingCsvHelper";
import {
    StreamingContextManager,
    FlowExecutionStrategy,
    StreamingComponentFactory,
    FlowComponents,
} from "./interfaces";
import { AugmentContextEngine } from "./augmentContextEngine";
import {
    BatchTransformer,
    StreamingTransformerWrapper,
} from "./batchTransformer";
import { FlowExecutionStrategyFactory } from "./flowExecutionStrategies";
import { DefaultStreamingComponentFactory } from "./streamingComponentFactory";
import * as path from "path";
import * as fs from "fs";

/**
 * Enhanced orchestrator with streaming capabilities and context management
 * Follows Dependency Inversion Principle: Depends on abstractions
 * Follows Single Responsibility Principle: Orchestration with streaming support
 */
export class EnhancedOrchestrator extends EtlOrchestrator {
    private contextManager: StreamingContextManager;
    private componentFactory: StreamingComponentFactory;
    private executionStrategy: FlowExecutionStrategy;
    private readonly DEFAULT_BATCH_SIZE = 10000;

    constructor(
        configManager: any,
        connectionService: any,
        extractorFactory: any,
        transformerFactory: any,
        loaderFactory: any,
        logger: any,
        // Dependency injection for streaming components
        contextManager?: StreamingContextManager,
        componentFactory?: StreamingComponentFactory,
        executionStrategy?: FlowExecutionStrategy
    ) {
        super(
            configManager,
            connectionService,
            extractorFactory,
            transformerFactory,
            loaderFactory,
            logger
        );

        // Use dependency injection or create defaults
        this.contextManager =
            contextManager || new AugmentContextEngine(logger);
        this.componentFactory =
            componentFactory ||
            new DefaultStreamingComponentFactory(logger, connectionService);
        this.executionStrategy =
            executionStrategy ||
            FlowExecutionStrategyFactory.create("simple", logger);
    }

    /**
     * Get logger instance (protected to allow access in this class)
     */
    protected getLogger(): any {
        return (this as any).logger;
    }

    /**
     * Override flow execution to use streaming context
     */
    protected async executeFlow(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loader: Loader,
        context: JobContext
    ): Promise<FlowResult> {
        const startTime = Date.now();

        // Create streaming context
        this.contextManager.createStreamContext(context.jobName, {
            batchSize: this.DEFAULT_BATCH_SIZE,
            globalVariables: context.globalVariables,
            recordLimit: context.recordLimit,
        });

        let totalRecords = 0;
        const allErrors: any[] = [];

        // Set up CSV saving for transformed data if debug enabled
        let transformedCsvPath: string | null = null;
        const stagingDir = path.join(process.cwd(), "staging", "transformed");
        if (!fs.existsSync(stagingDir)) {
            fs.mkdirSync(stagingDir, { recursive: true });
        }
        transformedCsvPath = path.join(
            stagingDir,
            `${flowName}_transformed_${Date.now()}.csv`
        );

        try {
            // Wrap transformer for batch processing if needed
            const batchTransformer =
                this.wrapTransformerForBatching(transformer);

            // Initialize batch transformer if needed
            if (batchTransformer.initialize) {
                await batchTransformer.initialize();
            }

            // Process in streaming batches
            for await (const extractedBatch of extractor.extract()) {
                // Transform batch
                const transformedBatch = batchTransformer.transformBatch
                    ? await batchTransformer.transformBatch(extractedBatch)
                    : await transformer.transform(extractedBatch);

                // Update context with flow output
                await this.contextManager.updateFlowOutput(
                    context.jobName,
                    flowName,
                    transformedBatch,
                    true
                );

                // Save transformed data if debug enabled
                if (transformedCsvPath && transformedBatch.length > 0) {
                    await StreamingCsvHelper.appendToCsv(
                        transformedBatch,
                        transformedCsvPath
                    );
                }

                // Load batch
                const loadResult = await loader.load(transformedBatch);
                totalRecords += transformedBatch.length;

                if (!loader.finalize) {
                    allErrors.push(...loadResult.errors);
                }

                this.getLogger().logBatchProgress(
                    `${flowName} processing`,
                    totalRecords,
                    totalRecords
                );

                // Create periodic snapshots for large jobs
                if (totalRecords % 50000 === 0) {
                    this.contextManager.createSnapshot(context.jobName);
                }
            }

            // Finalize batch transformer if needed
            if (batchTransformer.finalize) {
                const finalRecords = await batchTransformer.finalize();
                if (finalRecords.length > 0) {
                    const loadResult = await loader.load(finalRecords);
                    totalRecords += finalRecords.length;
                    allErrors.push(...loadResult.errors);
                }
            }

            // Finalize loader
            if (loader.finalize) {
                const finalizeResult = await loader.finalize();
                allErrors.push(...finalizeResult.errors);
            }

            const duration = Date.now() - startTime;
            const success = allErrors.length === 0;

            if (transformedCsvPath) {
                this.getLogger().info(
                    `Transformed data saved to: ${transformedCsvPath}`
                );
            }

            // Log memory stats
            const memoryStats = this.contextManager.getMemoryStats(
                context.jobName
            );
            this.getLogger().info(
                `Memory usage: ${memoryStats.memoryUsageMB.toFixed(
                    2
                )}MB, Total records: ${memoryStats.totalRecords}`
            );

            return {
                flowName,
                success,
                recordsProcessed: totalRecords,
                duration,
                outputs: { default: [] }, // Don't accumulate outputs to save memory
                errors: allErrors,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            this.getLogger().error(`Flow execution failed: ${flowName}`, error);

            return {
                flowName,
                success: false,
                recordsProcessed: totalRecords,
                duration,
                outputs: {},
                errors: [
                    {
                        message:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    },
                ],
            };
        } finally {
            // Cleanup context for completed flow
            await this.contextManager.cleanup(context.jobName);
        }
    }

    /**
     * Wrap transformer for batch processing if not already a batch transformer
     */
    private wrapTransformerForBatching(
        transformer: Transformer
    ): BatchTransformer {
        if ("transformBatch" in transformer) {
            return transformer as BatchTransformer;
        }
        return new StreamingTransformerWrapper(transformer);
    }

    /**
     * Get context manager for external access
     */
    getContextManager(): StreamingContextManager {
        return this.contextManager;
    }

    /**
     * Get context engine for backward compatibility
     */
    getContextEngine(): StreamingContextManager {
        return this.contextManager;
    }

    /**
     * Get active contexts
     */
    getActiveContexts(): string[] {
        return this.contextManager.getActiveContexts();
    }

    /**
     * Get memory statistics for a job
     */
    getMemoryStats(jobName: string) {
        return this.contextManager.getMemoryStats(jobName);
    }

    /**
     * Execute streaming flow with multiple targets
     */
    private async executeStreamingFlowWithTargets(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loaders: Map<string, Loader>,
        targets: TargetConfig[],
        context: JobContext,
        debugOptions?: { saveTransformerCsv?: boolean }
    ): Promise<FlowResult> {
        let totalRecords = 0;
        const allErrors: any[] = [];

        // Set up CSV saving for transformed data if debug enabled
        let transformedCsvPath: string | null = null;
        if (debugOptions?.saveTransformerCsv) {
            const stagingDir = path.join(
                process.cwd(),
                "staging",
                "transformed"
            );
            if (!fs.existsSync(stagingDir)) {
                fs.mkdirSync(stagingDir, { recursive: true });
            }
            transformedCsvPath = path.join(
                stagingDir,
                `${flowName}_transformed_${Date.now()}.csv`
            );
        }

        // Process in streaming batches
        for await (const extractedBatch of extractor.extract()) {
            const transformedBatch = await transformer.transform(
                extractedBatch
            );

            // Update context with flow output
            await this.contextManager.updateFlowOutput(
                context.jobName,
                flowName,
                transformedBatch,
                true
            );

            // Save transformed data if debug enabled
            if (transformedCsvPath && transformedBatch.length > 0) {
                await StreamingCsvHelper.appendToCsv(
                    transformedBatch,
                    transformedCsvPath
                );
            }

            // Load to all targets
            for (const targetConfig of targets) {
                const loader = loaders.get(targetConfig.name);
                if (!loader) continue;

                const loadResult = await loader.load(transformedBatch);
                if (loadResult.errors && loadResult.errors.length > 0) {
                    allErrors.push(...loadResult.errors);
                }
            }

            totalRecords += transformedBatch.length;
            this.getLogger().logBatchProgress(
                flowName,
                totalRecords,
                totalRecords
            );
        }

        // Finalize all loaders
        for (const [, loader] of loaders) {
            if (loader.finalize) {
                const finalResult = await loader.finalize();
                if (finalResult.errors && finalResult.errors.length > 0) {
                    allErrors.push(...finalResult.errors);
                }
            }
        }

        if (transformedCsvPath) {
            this.getLogger().info(
                `Transformed data saved to: ${transformedCsvPath}`
            );
        }

        return {
            flowName,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            outputs: {},
            errors: allErrors,
            duration: 0,
        };
    }
}
