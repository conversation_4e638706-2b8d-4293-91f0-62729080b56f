            transformedCsvPath = path.join(
                stagingDir, 
                `${flowName}_transformed_${Date.now()}.csv`
            );
        }

        try {
            // Process in streaming batches
            for await (const extractedBatch of extractor.extract()) {
                // Transform batch
                const transformedBatch = await transformer.transform(extractedBatch);

                // Save transformed data if debug enabled
                if (transformedCsvPath && transformedBatch.length > 0) {
                    await StreamingCsvHelper.appendToCsv(transformedBatch, transformedCsvPath);
                }

                // Load batch
                const loadResult = await loader.load(transformedBatch);
                totalRecords += transformedBatch.length;

                if (!loader.finalize) {
                    allErrors.push(...loadResult.errors);
                }

                this.logger.logBatchProgress(
                    `${flowName} processing`,
                    totalRecords,
                    totalRecords
                );
            }

            // Finalize loader
            if (loader.finalize) {
                const finalizeResult = await loader.finalize();
                allErrors.push(...finalizeResult.errors);
            }

            const duration = Date.now() - startTime;
            const success = allErrors.length === 0;

            if (transformedCsvPath) {
                this.logger.info(`Transformed data saved to: ${transformedCsvPath}`);
            }

            return {
                flowName,
                success,
                recordsProcessed: totalRecords,
                duration,
                outputs: { default: [] }, // Don't accumulate outputs to save memory
                errors: allErrors,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Flow execution failed: ${flowName}`, error);

            return {
                flowName,
                success: false,
                recordsProcessed: totalRecords,
                duration,
                outputs: {},
                errors: [
                    {
                        message:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    },
                ],
            };
        }
    }

    /**
     * Execute a simple flow (single source → transform → targets)
     */
    private async executeSimpleFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext
    ): Promise<FlowResult> {
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            migrationConfig.dataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias = primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        
        if (!primaryExtractor) {
            throw new Error(`Primary extractor '${primaryExtractorAlias}' not found`);
        }

        const transformer = this.transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        // Process in streaming fashion
        return this.executeStreamingFlowWithTargets(
            flowConfig.name,
            primaryExtractor,
            transformer,
            loaders,
            flowConfig.targets,
            context,
            flowConfig.debug
        );
    }

    /**
     * Execute a join flow with memory-efficient processing
     */
    private async executeJoinFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext
    ): Promise<FlowResult> {
        if (!isJoinTransform(flowConfig.transform)) {
            throw new Error("Invalid transform for executeJoinFlow");
        }

        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            migrationConfig.dataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const joinTransformConfig = flowConfig.transform;
        const primarySourceAlias = joinTransformConfig.primarySource;
        
        if (!extractors.has(primarySourceAlias)) {
            throw new Error(`Primary extractor '${primarySourceAlias}' not found`);
        }

        const transformer = this.transformerFactory.createTransformer(
            joinTransformConfig,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        ) as JoinTransformer;

        // Load secondary data sources (reference data)
        for (const [alias, extractor] of extractors) {
            if (alias !== primarySourceAlias) {
                this.logger.info(`Loading reference data for alias: ${alias}`);
                const referenceData: Record<string, any>[] = [];
                let recordCount = 0;
                
                for await (const batch of extractor.extract()) {
                    for (const record of batch) {
                        if (recordCount >= this.MAX_REFERENCE_RECORDS) {
                            this.logger.warn(
                                `Reference data ${alias} exceeds ${this.MAX_REFERENCE_RECORDS} records`
                            );
                            break;
                        }
                        referenceData.push(record);
                        recordCount++;
                    }
                    if (recordCount >= this.MAX_REFERENCE_RECORDS) break;
                }
                
                transformer.setSourceData(alias, referenceData);
                context.flowOutputs.set(alias, referenceData);
            }
        }

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        const primaryExtractor = extractors.get(primarySourceAlias)!;

        // Process primary source in streaming fashion
        return this.executeStreamingFlowWithTargets(
            flowConfig.name,
            primaryExtractor,
            transformer,
            loaders,
            flowConfig.targets,
            context,
            flowConfig.debug
        );
    }

    /**
     * Execute a split flow
     */
    private async executeSplitFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext
    ): Promise<FlowResult> {
        if (!isSplitTransform(flowConfig.transform)) {
            throw new Error("Invalid transform for executeSplitFlow");
        }

        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            migrationConfig.dataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias = primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        
        if (!primaryExtractor) {
            throw new Error(`Primary extractor '${primaryExtractorAlias}' not found`);
        }

        const transformer = this.transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        ) as SplitTransformer;

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        let totalRecords = 0;
        const allErrors: any[] = [];

        // Process in streaming batches
        for await (const batch of primaryExtractor.extract()) {
            const splitOutputs = await transformer.transform(batch) as unknown as Map<string, Record<string, any>[]>;
            
            // Load to appropriate targets
            for (const [targetName, records] of splitOutputs) {
                const loader = loaders.get(targetName);
                if (loader && records.length > 0) {
                    const loadResult = await loader.load(records);
                    if (loadResult.errors) {
                        allErrors.push(...loadResult.errors);
                    }
                    totalRecords += records.length;
                }
            }
        }

        // Finalize loaders
        for (const [targetName, loader] of loaders) {
            if (loader.finalize) {
                const finalResult = await loader.finalize();
                if (finalResult.errors) {
                    allErrors.push(...finalResult.errors);
                }
            }
        }

        return {
            flowName: flowConfig.name,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            outputs: {},
            errors: allErrors,
            duration: 0,
        };
    }

    /**
     * Execute a lookup flow
     */
    private async executeLookupFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext
    ): Promise<FlowResult> {
        if (!isLookupTransform(flowConfig.transform)) {
            throw new Error("Invalid transform type for executeLookupFlow");
        }

        const lookupTransformConfig = flowConfig.transform;
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            migrationConfig.dataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias = primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        
        if (!primaryExtractor) {
            throw new Error(`Primary extractor '${primaryExtractorAlias}' not found`);
        }

        const transformer = this.transformerFactory.createTransformer(
            lookupTransformConfig,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        ) as LookupTransformer;

        // Load lookup data if specified
        if (lookupTransformConfig.sourceAlias) {
            const lookupExtractor = extractors.get(lookupTransformConfig.sourceAlias);
            if (lookupExtractor) {
                const lookupData: Record<string, any>[] = [];
                let recordCount = 0;
                
                for await (const batch of lookupExtractor.extract()) {
                    for (const record of batch) {
                        if (recordCount >= this.MAX_REFERENCE_RECORDS) {
                            this.logger.warn(
                                `Lookup data exceeds ${this.MAX_REFERENCE_RECORDS} records`
                            );
                            break;
                        }
                        lookupData.push(record);
                        recordCount++;
                    }
                    if (recordCount >= this.MAX_REFERENCE_RECORDS) break;
                }
                
                transformer.setLookupData(lookupData);
            }
        }

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        return this.executeStreamingFlowWithTargets(
            flowConfig.name,
            primaryExtractor,
            transformer,
            loaders,
            flowConfig.targets,
            context,
            flowConfig.debug
        );
    }

    /**
     * Execute an aggregation flow
     */
    private async executeAggregationFlow(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext
    ): Promise<FlowResult> {
        const extractors = await this.extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            migrationConfig.dataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias = primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        
        if (!primaryExtractor) {
            throw new Error(`Primary extractor '${primaryExtractorAlias}' not found`);
        }

        const transformer = this.transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );

        const loaders = await this.loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        // For aggregation, we need to collect all records first
        // Consider using temporary file storage for very large datasets
        const allRecords: Record<string, any>[] = [];
        
        for await (const batch of primaryExtractor.extract()) {
            allRecords.push(...batch);
            
            // If getting too large, consider writing to temp file
            if (allRecords.length > 500000) {
                this.logger.warn(
                    "Aggregation flow processing large dataset. Consider using database for aggregation."
                );
            }
        }

        // Transform all records
        const transformedRecords = await transformer.transform(allRecords);

        // Load results
        let totalRecords = 0;
        const allErrors: any[] = [];

        for (const targetConfig of flowConfig.targets) {
            const loader = loaders.get(targetConfig.name);
            if (!loader) continue;
            
            const loadResult = await loader.load(transformedRecords);
            if (loadResult.errors) {
                allErrors.push(...loadResult.errors);
            }
            totalRecords += transformedRecords.length;
        }

        // Finalize loaders
        for (const [targetName, loader] of loaders) {
            if (loader.finalize) {
                const finalResult = await loader.finalize();
                if (finalResult.errors) {
                    allErrors.push(...finalResult.errors);
                }
            }
        }

        return {
            flowName: flowConfig.name,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            outputs: {},
            errors: allErrors,
            duration: 0,
        };
    }

    /**
     * Execute streaming flow with multiple targets
     */
    private async executeStreamingFlowWithTargets(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loaders: Map<string, Loader>,
        targets: TargetConfig[],
        context: JobContext,
        debugOptions?: { saveTransformerCsv?: boolean }
    ): Promise<FlowResult> {
        let totalRecords = 0;
        const allErrors: any[] = [];

        // Set up CSV saving for transformed data if debug enabled
        let transformedCsvPath: string | null = null;
        if (debugOptions?.saveTransformerCsv) {
            const stagingDir = path.join(process.cwd(), 'staging', 'transformed');
            if (!fs.existsSync(stagingDir)) {
                fs.mkdirSync(stagingDir, { recursive: true });
            }
            transformedCsvPath = path.join(
                stagingDir, 
                `${flowName}_transformed_${Date.now()}.csv`
            );
        }

        // Process in streaming batches
        for await (const extractedBatch of extractor.extract()) {
            const transformedBatch = await transformer.transform(extractedBatch);
            
            // Save transformed data if debug enabled
            if (transformedCsvPath && transformedBatch.length > 0) {
                await StreamingCsvHelper.appendToCsv(transformedBatch, transformedCsvPath);
            }

            // Load to all targets
            for (const targetConfig of targets) {
                const loader = loaders.get(targetConfig.name);
                if (!loader) continue;
                
                const loadResult = await loader.load(transformedBatch);
                if (loadResult.errors && loadResult.errors.length > 0) {
                    allErrors.push(...loadResult.errors);
                }
            }
            
            totalRecords += transformedBatch.length;
            this.logger.logBatchProgress(flowName, totalRecords, totalRecords);
        }

        // Finalize all loaders
        for (const [targetName, loader] of loaders) {
            if (loader.finalize) {
                const finalResult = await loader.finalize();
                if (finalResult.errors && finalResult.errors.length > 0) {
                    allErrors.push(...finalResult.errors);
                }
            }
        }

        if (transformedCsvPath) {
            this.logger.info(`Transformed data saved to: ${transformedCsvPath}`);
        }

        return {
            flowName,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            outputs: {},
            errors: allErrors,
            duration: 0,
        };
    }

    /**
     * Test connections for a configuration
     */
    async testConnections(config: EtlConfig): Promise<Record<string, boolean>> {
        const results: Record<string, boolean> = {};
        let connections: Record<string, any> = {};

        if (isSimpleJobConfig(config)) {
            connections = config.connections || {};
        } else if (isMigrationConfig(config)) {
            connections = config.connections;
        }

        for (const [name, connectionConfig] of Object.entries(connections)) {
            try {
                const isValid = await this.connectionService.testConnection(connectionConfig);
                results[name] = isValid;
            } catch (error) {
                this.logger.error(`Connection test failed for ${name}`, error);
                results[name] = false;
            }
        }

        return results;
    }

    /**
     * Get flow execution order (for migration configs)
     */
    getFlowExecutionOrder(configPath: string): string[] {
        return this.configManager.listFlows(configPath);
    }

    /**
     * Validate configuration before execution
     */
    validateConfiguration(configPath: string): string[] {
        const validation = this.configManager.validateConfigFile(configPath);
        return validation.errors;
    }
}
