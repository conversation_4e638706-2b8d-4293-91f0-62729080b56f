import { EtlOrchestrator } from "../core/orchestrator";
import {
    FlowConfig,
    MigrationConfig,
    TargetConfig,
    isJoinTransform,
    isSplitTransform,
    isLookupTransform,
    isAggregationTransform,
} from "../config/schema";
import {
    Extractor,
    Transformer,
    Loader,
    FlowResult,
    JobContext,
} from "../core/interfaces";
import { StreamingCsvHelper } from "./streamingCsvHelper";
import { AugmentContextEngine, StreamContext } from "./augmentContextEngine";
import {
    BatchTransformer,
    StreamingTransformerWrapper,
} from "./batchTransformer";
import { SplitTransformer } from "../transformers/splitTransformer";
import { JoinTransformer } from "../transformers/joinTransformer";
import { LookupTransformer } from "../transformers/lookupTransformer";
import * as path from "path";
import * as fs from "fs";

/**
 * Enhanced orchestrator with streaming capabilities and context management
 * Integrates AugmentContextEngine for memory-efficient processing
 */
export class EnhancedOrchestrator extends EtlOrchestrator {
    private contextEngine: AugmentContextEngine;
    private readonly DEFAULT_BATCH_SIZE = 10000;
    protected logger: any; // Override to make accessible

    constructor(
        configManager: any,
        connectionService: any,
        extractorFactory: any,
        transformerFactory: any,
        loaderFactory: any,
        logger: any
    ) {
        super(
            configManager,
            connectionService,
            extractorFactory,
            transformerFactory,
            loaderFactory,
            logger
        );
        this.contextEngine = new AugmentContextEngine(logger);
        this.logger = logger; // Store logger for access
    }

    /**
     * Override flow execution to use streaming context
     */
    protected async executeFlow(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loader: Loader,
        context: JobContext
    ): Promise<FlowResult> {
        const startTime = Date.now();

        // Create streaming context
        this.contextEngine.createStreamContext(context.jobName, {
            batchSize: this.DEFAULT_BATCH_SIZE,
            globalVariables: context.globalVariables,
            recordLimit: context.recordLimit,
        });

        let totalRecords = 0;
        const allErrors: any[] = [];

        // Set up CSV saving for transformed data if debug enabled
        let transformedCsvPath: string | null = null;
        const stagingDir = path.join(process.cwd(), "staging", "transformed");
        if (!fs.existsSync(stagingDir)) {
            fs.mkdirSync(stagingDir, { recursive: true });
        }
        transformedCsvPath = path.join(
            stagingDir,
            `${flowName}_transformed_${Date.now()}.csv`
        );

        try {
            // Wrap transformer for batch processing if needed
            const batchTransformer =
                this.wrapTransformerForBatching(transformer);

            // Initialize batch transformer if needed
            if (batchTransformer.initialize) {
                await batchTransformer.initialize();
            }

            // Process in streaming batches
            for await (const extractedBatch of extractor.extract()) {
                // Transform batch
                const transformedBatch = batchTransformer.transformBatch
                    ? await batchTransformer.transformBatch(extractedBatch)
                    : await transformer.transform(extractedBatch);

                // Update context with flow output
                await this.contextEngine.updateFlowOutput(
                    context.jobName,
                    flowName,
                    transformedBatch,
                    true
                );

                // Save transformed data if debug enabled
                if (transformedCsvPath && transformedBatch.length > 0) {
                    await StreamingCsvHelper.appendToCsv(
                        transformedBatch,
                        transformedCsvPath
                    );
                }

                // Load batch
                const loadResult = await loader.load(transformedBatch);
                totalRecords += transformedBatch.length;

                if (!loader.finalize) {
                    allErrors.push(...loadResult.errors);
                }

                this.logger.logBatchProgress(
                    `${flowName} processing`,
                    totalRecords,
                    totalRecords
                );

                // Create periodic snapshots for large jobs
                if (totalRecords % 50000 === 0) {
                    this.contextEngine.createSnapshot(context.jobName);
                }
            }

            // Finalize batch transformer if needed
            if (batchTransformer.finalize) {
                const finalRecords = await batchTransformer.finalize();
                if (finalRecords.length > 0) {
                    const loadResult = await loader.load(finalRecords);
                    totalRecords += finalRecords.length;
                    allErrors.push(...loadResult.errors);
                }
            }

            // Finalize loader
            if (loader.finalize) {
                const finalizeResult = await loader.finalize();
                allErrors.push(...finalizeResult.errors);
            }

            const duration = Date.now() - startTime;
            const success = allErrors.length === 0;

            if (transformedCsvPath) {
                this.logger.info(
                    `Transformed data saved to: ${transformedCsvPath}`
                );
            }

            // Log memory stats
            const memoryStats = this.contextEngine.getMemoryStats(
                context.jobName
            );
            this.logger.info(
                `Memory usage: ${memoryStats.memoryUsageMB.toFixed(
                    2
                )}MB, Total records: ${memoryStats.totalRecords}`
            );

            return {
                flowName,
                success,
                recordsProcessed: totalRecords,
                duration,
                outputs: { default: [] }, // Don't accumulate outputs to save memory
                errors: allErrors,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Flow execution failed: ${flowName}`, error);

            return {
                flowName,
                success: false,
                recordsProcessed: totalRecords,
                duration,
                outputs: {},
                errors: [
                    {
                        message:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    },
                ],
            };
        } finally {
            // Cleanup context for completed flow
            await this.contextEngine.cleanup(context.jobName);
        }
    }

    /**
     * Wrap transformer for batch processing if not already a batch transformer
     */
    private wrapTransformerForBatching(
        transformer: Transformer
    ): BatchTransformer {
        if ("transformBatch" in transformer) {
            return transformer as BatchTransformer;
        }
        return new StreamingTransformerWrapper(transformer);
    }

    /**
     * Get context engine for external access
     */
    getContextEngine(): AugmentContextEngine {
        return this.contextEngine;
    }

    /**
     * Get active contexts
     */
    getActiveContexts(): string[] {
        return this.contextEngine.getActiveContexts();
    }

    /**
     * Get memory statistics for a job
     */
    getMemoryStats(jobName: string) {
        return this.contextEngine.getMemoryStats(jobName);
    }

    /**
     * Execute streaming flow with multiple targets
     */
    private async executeStreamingFlowWithTargets(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loaders: Map<string, Loader>,
        targets: TargetConfig[],
        context: JobContext,
        debugOptions?: { saveTransformerCsv?: boolean }
    ): Promise<FlowResult> {
        let totalRecords = 0;
        const allErrors: any[] = [];

        // Set up CSV saving for transformed data if debug enabled
        let transformedCsvPath: string | null = null;
        if (debugOptions?.saveTransformerCsv) {
            const stagingDir = path.join(
                process.cwd(),
                "staging",
                "transformed"
            );
            if (!fs.existsSync(stagingDir)) {
                fs.mkdirSync(stagingDir, { recursive: true });
            }
            transformedCsvPath = path.join(
                stagingDir,
                `${flowName}_transformed_${Date.now()}.csv`
            );
        }

        // Process in streaming batches
        for await (const extractedBatch of extractor.extract()) {
            const transformedBatch = await transformer.transform(
                extractedBatch
            );

            // Update context with flow output
            await this.contextEngine.updateFlowOutput(
                context.jobName,
                flowName,
                transformedBatch,
                true
            );

            // Save transformed data if debug enabled
            if (transformedCsvPath && transformedBatch.length > 0) {
                await StreamingCsvHelper.appendToCsv(
                    transformedBatch,
                    transformedCsvPath
                );
            }

            // Load to all targets
            for (const targetConfig of targets) {
                const loader = loaders.get(targetConfig.name);
                if (!loader) continue;

                const loadResult = await loader.load(transformedBatch);
                if (loadResult.errors && loadResult.errors.length > 0) {
                    allErrors.push(...loadResult.errors);
                }
            }

            totalRecords += transformedBatch.length;
            this.logger.logBatchProgress(flowName, totalRecords, totalRecords);
        }

        // Finalize all loaders
        for (const [, loader] of loaders) {
            if (loader.finalize) {
                const finalResult = await loader.finalize();
                if (finalResult.errors && finalResult.errors.length > 0) {
                    allErrors.push(...finalResult.errors);
                }
            }
        }

        if (transformedCsvPath) {
            this.logger.info(
                `Transformed data saved to: ${transformedCsvPath}`
            );
        }

        return {
            flowName,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            outputs: {},
            errors: allErrors,
            duration: 0,
        };
    }
}
