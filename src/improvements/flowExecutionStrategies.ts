import { 
    FlowExecutionStrategy, 
    FlowComponents, 
    ProcessingMetrics,
    StreamingEvent 
} from './interfaces';
import { FlowResult, JobContext } from '../core/interfaces';
import { BaseStreamingComponent, StreamingUtils } from './baseStreamingComponent';
import { Logger } from '../utils/logger';

/**
 * Abstract base strategy for flow execution
 * Follows Strategy Pattern and Template Method Pattern
 */
abstract class BaseFlowExecutionStrategy extends BaseStreamingComponent implements FlowExecutionStrategy {
    constructor(logger: Logger) {
        super(logger);
    }

    /**
     * Template method for flow execution
     * Defines the skeleton of the algorithm
     */
    async executeFlow(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<FlowResult> {
        const startTime = Date.now();
        let totalRecords = 0;
        const allErrors: any[] = [];

        try {
            // Pre-execution hook
            await this.preExecution(flowName, context, components);

            // Main execution logic (implemented by subclasses)
            const result = await this.doExecuteFlow(flowName, context, components);
            
            totalRecords = result.recordsProcessed;
            allErrors.push(...result.errors);

            // Post-execution hook
            await this.postExecution(flowName, context, components, result);

            const duration = Date.now() - startTime;
            
            // Emit completion event
            this.emit(this.createEvent('flow.completed', {
                flowName,
                recordsProcessed: totalRecords,
                duration,
                success: result.success
            }, context.jobName));

            return {
                ...result,
                duration
            };

        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Flow execution failed: ${flowName}`, error);

            // Emit error event
            this.emit(this.createEvent('flow.error', {
                flowName,
                error: error instanceof Error ? error.message : String(error),
                duration
            }, context.jobName));

            return {
                flowName,
                success: false,
                recordsProcessed: totalRecords,
                duration,
                outputs: {},
                errors: [
                    {
                        message: error instanceof Error ? error.message : String(error),
                    },
                ],
            };
        }
    }

    /**
     * Abstract method to be implemented by concrete strategies
     */
    protected abstract doExecuteFlow(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<FlowResult>;

    /**
     * Pre-execution hook (can be overridden)
     */
    protected async preExecution(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<void> {
        this.logger.debug(`Starting execution of flow: ${flowName}`);
        
        // Initialize components if needed
        if (components.transformer.initialize) {
            await components.transformer.initialize();
        }
    }

    /**
     * Post-execution hook (can be overridden)
     */
    protected async postExecution(
        flowName: string,
        context: JobContext,
        components: FlowComponents,
        result: FlowResult
    ): Promise<void> {
        // Finalize transformer if needed
        if (components.transformer.finalize) {
            const finalRecords = await components.transformer.finalize();
            if (finalRecords.length > 0) {
                const loadResult = await components.loader.load(finalRecords);
                result.recordsProcessed += finalRecords.length;
                result.errors.push(...loadResult.errors);
            }
        }

        // Finalize loader
        if (components.loader.finalize) {
            const finalizeResult = await components.loader.finalize();
            result.errors.push(...finalizeResult.errors);
        }

        this.logger.debug(`Completed execution of flow: ${flowName}`);
    }
}

/**
 * Simple streaming strategy
 * Processes data in batches without complex dependencies
 */
export class SimpleStreamingStrategy extends BaseFlowExecutionStrategy {
    protected async doExecuteFlow(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<FlowResult> {
        let totalRecords = 0;
        const allErrors: any[] = [];

        // Process in streaming batches
        for await (const extractedBatch of components.extractor.extract()) {
            // Check record limit
            if (context.recordLimit && totalRecords >= context.recordLimit) {
                this.logger.info(
                    `Record limit of ${context.recordLimit} reached. Stopping at ${totalRecords} records.`
                );
                break;
            }

            // Transform batch
            const transformedBatch = await components.transformer.processBatch(extractedBatch);

            // Load batch
            const loadResult = await components.loader.load(transformedBatch);
            totalRecords += transformedBatch.length;
            allErrors.push(...loadResult.errors);

            // Emit progress event
            this.emit(this.createEvent('batch.processed', {
                flowName,
                batchSize: transformedBatch.length,
                totalRecords
            }, context.jobName));

            this.logger.debug(`Processed batch: ${transformedBatch.length} records (total: ${totalRecords})`);
        }

        return {
            flowName,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            duration: 0, // Will be set by template method
            outputs: { default: [] }, // Don't accumulate outputs to save memory
            errors: allErrors,
        };
    }
}

/**
 * Memory-aware streaming strategy
 * Monitors memory usage and adjusts batch sizes dynamically
 */
export class MemoryAwareStreamingStrategy extends BaseFlowExecutionStrategy {
    private readonly MEMORY_THRESHOLD_MB = 500; // 500MB threshold
    private readonly MIN_BATCH_SIZE = 1000;
    private readonly MAX_BATCH_SIZE = 50000;
    private currentBatchSize = 10000;

    protected async doExecuteFlow(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<FlowResult> {
        let totalRecords = 0;
        const allErrors: any[] = [];

        for await (const extractedBatch of components.extractor.extract()) {
            // Check record limit
            if (context.recordLimit && totalRecords >= context.recordLimit) {
                break;
            }

            // Adjust batch size based on memory usage
            const adjustedBatch = this.adjustBatchSize(extractedBatch);

            // Transform batch
            const transformedBatch = await components.transformer.processBatch(adjustedBatch);

            // Monitor memory usage
            const memoryUsage = StreamingUtils.estimateMemoryUsage(transformedBatch);
            this.adjustBatchSizeBasedOnMemory(memoryUsage);

            // Load batch
            const loadResult = await components.loader.load(transformedBatch);
            totalRecords += transformedBatch.length;
            allErrors.push(...loadResult.errors);

            // Emit memory usage event
            this.emit(this.createEvent('memory.usage', {
                flowName,
                memoryUsageMB: memoryUsage,
                batchSize: this.currentBatchSize,
                totalRecords
            }, context.jobName));

            this.logger.debug(
                `Processed batch: ${transformedBatch.length} records, ` +
                `Memory: ${memoryUsage.toFixed(2)}MB, ` +
                `Next batch size: ${this.currentBatchSize}`
            );
        }

        return {
            flowName,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            duration: 0,
            outputs: { default: [] },
            errors: allErrors,
        };
    }

    private adjustBatchSize(batch: any[]): any[] {
        if (batch.length <= this.currentBatchSize) {
            return batch;
        }
        return batch.slice(0, this.currentBatchSize);
    }

    private adjustBatchSizeBasedOnMemory(memoryUsageMB: number): void {
        if (memoryUsageMB > this.MEMORY_THRESHOLD_MB) {
            // Reduce batch size if memory usage is high
            this.currentBatchSize = Math.max(
                this.MIN_BATCH_SIZE,
                Math.floor(this.currentBatchSize * 0.8)
            );
        } else if (memoryUsageMB < this.MEMORY_THRESHOLD_MB * 0.5) {
            // Increase batch size if memory usage is low
            this.currentBatchSize = Math.min(
                this.MAX_BATCH_SIZE,
                Math.floor(this.currentBatchSize * 1.2)
            );
        }
    }
}

/**
 * Parallel processing strategy
 * Processes multiple batches in parallel (with concurrency control)
 */
export class ParallelStreamingStrategy extends BaseFlowExecutionStrategy {
    private readonly maxConcurrency: number;

    constructor(logger: Logger, maxConcurrency: number = 3) {
        super(logger);
        this.maxConcurrency = maxConcurrency;
    }

    protected async doExecuteFlow(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<FlowResult> {
        let totalRecords = 0;
        const allErrors: any[] = [];
        const activeBatches: Promise<any>[] = [];

        for await (const extractedBatch of components.extractor.extract()) {
            // Check record limit
            if (context.recordLimit && totalRecords >= context.recordLimit) {
                break;
            }

            // Process batch asynchronously
            const batchPromise = this.processBatchAsync(
                extractedBatch,
                components,
                flowName,
                context.jobName
            );

            activeBatches.push(batchPromise);

            // Wait for some batches to complete if we hit concurrency limit
            if (activeBatches.length >= this.maxConcurrency) {
                const completedBatch = await Promise.race(activeBatches);
                const index = activeBatches.findIndex(p => p === completedBatch);
                activeBatches.splice(index, 1);

                const result = await completedBatch;
                totalRecords += result.recordsProcessed;
                allErrors.push(...result.errors);
            }
        }

        // Wait for all remaining batches to complete
        const remainingResults = await Promise.all(activeBatches);
        for (const result of remainingResults) {
            totalRecords += result.recordsProcessed;
            allErrors.push(...result.errors);
        }

        return {
            flowName,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            duration: 0,
            outputs: { default: [] },
            errors: allErrors,
        };
    }

    private async processBatchAsync(
        batch: any[],
        components: FlowComponents,
        flowName: string,
        jobName: string
    ): Promise<{ recordsProcessed: number; errors: any[] }> {
        try {
            const transformedBatch = await components.transformer.processBatch(batch);
            const loadResult = await components.loader.load(transformedBatch);

            this.emit(this.createEvent('batch.completed', {
                flowName,
                batchSize: transformedBatch.length
            }, jobName));

            return {
                recordsProcessed: transformedBatch.length,
                errors: loadResult.errors
            };
        } catch (error) {
            this.logger.error('Batch processing failed:', error);
            return {
                recordsProcessed: 0,
                errors: [{ message: error instanceof Error ? error.message : String(error) }]
            };
        }
    }
}

/**
 * Strategy factory for creating execution strategies
 * Follows Factory Pattern
 */
export class FlowExecutionStrategyFactory {
    static create(
        strategyType: 'simple' | 'memory-aware' | 'parallel',
        logger: Logger,
        options?: any
    ): FlowExecutionStrategy {
        switch (strategyType) {
            case 'simple':
                return new SimpleStreamingStrategy(logger);
            
            case 'memory-aware':
                return new MemoryAwareStreamingStrategy(logger);
            
            case 'parallel':
                return new ParallelStreamingStrategy(
                    logger,
                    options?.maxConcurrency || 3
                );
            
            default:
                throw new Error(`Unknown strategy type: ${strategyType}`);
        }
    }
}
