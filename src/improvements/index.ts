import { EnhancedOrchestrator } from "./enhancedOrchestrator";
import { EnhancedLoaderFactory } from "./enhancedLoaderFactory";
import { AugmentContextEngine } from "./augmentContextEngine";
import { ConfigManager } from "../config/manager";
import { ConnectionService } from "../services/connectionService";
import { ExtractorFactory } from "../extractors/extractorFactory";
import { TransformerFactory } from "../transformers/transformerFactory";
import { Logger } from "../utils/logger";

/**
 * Factory function to create enhanced ETL orchestrator with streaming support
 */
export function createEnhancedEtlOrchestrator(
    logLevel?: string,
    options?: {
        enablePersistence?: boolean;
        tempDirectory?: string;
        maxMemoryRecords?: number;
    }
): EnhancedOrchestrator {
    const logger = new Logger(logLevel);
    const configManager = new ConfigManager(logger);
    const connectionService = new ConnectionService(logger);
    const extractorFactory = new ExtractorFactory(connectionService, logger);
    const transformerFactory = new TransformerFactory(logger);

    // Use enhanced loader factory for streaming bulk operations
    const loaderFactory = new EnhancedLoaderFactory(connectionService, logger);

    const orchestrator = new EnhancedOrchestrator(
        configManager,
        connectionService,
        extractorFactory,
        transformerFactory,
        loaderFactory as any, // Cast to match interface
        logger
    );

    // Configure context engine if options provided
    if (options) {
        const contextEngine = orchestrator.getContextEngine();
        // Additional configuration could be added here
    }

    return orchestrator;
}

/**
 * Run enhanced ETL job from configuration file
 */
export async function runEnhancedEtlJob(
    configPath: string,
    options?: {
        logLevel?: string;
        recordLimit?: number;
    }
): Promise<void> {
    const orchestrator = createEnhancedEtlOrchestrator(options?.logLevel);
    const result = await orchestrator.executeFromFile(
        configPath,
        options?.recordLimit
    );

    if (!result.success) {
        throw new Error(
            `ETL job failed: ${result.errors
                .map((e) => e.message || e)
                .join(", ")}`
        );
    }
}

/**
 * Validate ETL configuration file
 */
export function validateEnhancedConfig(
    configPath: string,
    logLevel?: string
): {
    valid: boolean;
    errors: string[];
} {
    const orchestrator = createEnhancedEtlOrchestrator(logLevel);
    const errors = orchestrator.validateConfiguration(configPath);

    return {
        valid: errors.length === 0,
        errors,
    };
}

// Re-export components
export { EnhancedOrchestrator } from "./enhancedOrchestrator";
export { EnhancedBulkLoader } from "./enhancedBulkLoader";
export { EnhancedLoaderFactory } from "./enhancedLoaderFactory";
export { StreamingCsvHelper } from "./streamingCsvHelper";
export { AugmentContextEngine } from "./augmentContextEngine";
export {
    BatchTransformer,
    StreamingTransformerWrapper,
} from "./batchTransformer";
export { MemoryEfficientFlowExecutors } from "./memoryEfficientFlowExecutors";
export { StreamingCsvExtractor } from "./streamingCsvExtractor";
export { runStreamingTests } from "./streamingTest";

// Export configuration types from parent
export * from "../config/schema";
export * from "../core/interfaces";
