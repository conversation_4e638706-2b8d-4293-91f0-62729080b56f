/**
 * Enhanced interfaces following SOLID principles
 * Segregated interfaces for specific responsibilities
 */

import { JobContext, FlowResult } from '../core/interfaces';

/**
 * Interface for memory monitoring capabilities
 * Single Responsibility: Memory usage tracking
 */
export interface MemoryMonitor {
    getMemoryStats(jobName: string): MemoryStats;
    getActiveContexts(): string[];
}

/**
 * Interface for context persistence
 * Single Responsibility: Data persistence operations
 */
export interface ContextPersistence {
    persistFlowOutput(jobName: string, flowName: string, records: Record<string, any>[]): Promise<void>;
    loadPersistedFlowOutput(jobName: string, flowName: string): Promise<Record<string, any>[]>;
    cleanup(jobName: string): Promise<void>;
}

/**
 * Interface for context snapshots
 * Single Responsibility: State capture and recovery
 */
export interface ContextSnapshots {
    createSnapshot(jobName: string): ContextSnapshot;
    getSnapshots(jobName: string): ContextSnapshot[];
}

/**
 * Interface for streaming context management
 * Single Responsibility: Context lifecycle management
 */
export interface StreamingContextManager extends MemoryMonitor, ContextPersistence, ContextSnapshots {
    createStreamContext(jobName: string, options: StreamContextOptions): StreamContext;
    updateFlowOutput(jobName: string, flowName: string, records: Record<string, any>[], append?: boolean): Promise<void>;
    getFlowOutput(jobName: string, flowName: string): Promise<Record<string, any>[]>;
}

/**
 * Interface for batch processing capabilities
 * Single Responsibility: Batch data processing
 */
export interface BatchProcessor<T, R> {
    processBatch(batch: T[]): Promise<R[]>;
    initialize?(): Promise<void>;
    finalize?(): Promise<R[]>;
}

/**
 * Interface for streaming data extraction
 * Single Responsibility: Data extraction with streaming
 */
export interface StreamingExtractor<T> {
    extract(): AsyncGenerator<T[], void, unknown>;
    getRecordCount?(): Promise<number>;
    getFileStats?(): FileStats;
}

/**
 * Interface for memory-efficient loading
 * Single Responsibility: Efficient data loading
 */
export interface MemoryEfficientLoader<T> {
    load(records: T[]): Promise<LoadResult>;
    finalize?(): Promise<LoadResult>;
    getLoadStats?(): LoadStats;
}

/**
 * Interface for flow execution strategies
 * Single Responsibility: Flow execution logic
 */
export interface FlowExecutionStrategy {
    executeFlow(
        flowName: string,
        context: JobContext,
        components: FlowComponents
    ): Promise<FlowResult>;
}

/**
 * Supporting types and interfaces
 */
export interface MemoryStats {
    totalRecords: number;
    flowCounts: Record<string, number>;
    memoryUsageMB: number;
}

export interface ContextSnapshot {
    jobName: string;
    flowOutputs: Record<string, any[]>;
    globalVariables: Record<string, any>;
    timestamp: number;
    recordCounts: Record<string, number>;
}

export interface StreamContextOptions {
    batchSize?: number;
    maxMemoryRecords?: number;
    globalVariables?: Record<string, any>;
    recordLimit?: number;
    enablePersistence?: boolean;
    tempDirectory?: string;
}

export interface StreamContext extends JobContext {
    batchSize: number;
    maxMemoryRecords: number;
    tempDirectory: string;
    enablePersistence: boolean;
}

export interface FileStats {
    sizeInMB: number;
    path: string;
    recommendStreaming: boolean;
}

export interface LoadResult {
    recordsProcessed: number;
    recordsSucceeded: number;
    recordsFailed: number;
    errors: any[];
    duration?: number;
    results?: any[];
    success: boolean;
}

export interface LoadStats {
    totalBatches: number;
    totalRecords: number;
    averageBatchSize: number;
    totalDuration: number;
}

export interface FlowComponents {
    extractor: StreamingExtractor<any>;
    transformer: BatchProcessor<any, any>;
    loader: MemoryEfficientLoader<any>;
}

/**
 * Configuration interfaces following Open/Closed Principle
 */
export interface StreamingConfiguration {
    readonly batchSize: number;
    readonly maxMemoryRecords: number;
    readonly enablePersistence: boolean;
    readonly tempDirectory: string;
}

export interface ProcessingMetrics {
    readonly startTime: number;
    readonly recordsProcessed: number;
    readonly batchesProcessed: number;
    readonly errorsEncountered: number;
}

/**
 * Factory interface for creating streaming components
 * Dependency Inversion Principle: Depend on abstractions
 */
export interface StreamingComponentFactory {
    createExtractor(config: any): StreamingExtractor<any>;
    createTransformer(config: any): BatchProcessor<any, any>;
    createLoader(config: any): MemoryEfficientLoader<any>;
    createContextManager(options?: StreamContextOptions): StreamingContextManager;
}

/**
 * Event interfaces for observability
 * Single Responsibility: Event handling
 */
export interface StreamingEvent {
    readonly type: string;
    readonly timestamp: number;
    readonly jobName: string;
    readonly data: any;
}

export interface EventEmitter {
    emit(event: StreamingEvent): void;
    on(eventType: string, handler: (event: StreamingEvent) => void): void;
    off(eventType: string, handler: (event: StreamingEvent) => void): void;
}

/**
 * Validation interfaces
 * Single Responsibility: Data validation
 */
export interface DataValidator<T> {
    validate(data: T): ValidationResult;
    validateBatch(batch: T[]): ValidationResult[];
}

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
}

/**
 * Error handling interfaces
 * Single Responsibility: Error management
 */
export interface ErrorHandler {
    handleError(error: Error, context: ErrorContext): Promise<void>;
    shouldRetry(error: Error): boolean;
    getRetryDelay(attemptNumber: number): number;
}

export interface ErrorContext {
    jobName: string;
    flowName: string;
    batchNumber: number;
    recordCount: number;
}

/**
 * Resource management interface
 * Single Responsibility: Resource lifecycle
 */
export interface ResourceManager {
    allocate(resourceType: string, amount: number): Promise<boolean>;
    release(resourceType: string, amount: number): Promise<void>;
    getUsage(resourceType: string): number;
    getAvailable(resourceType: string): number;
}
