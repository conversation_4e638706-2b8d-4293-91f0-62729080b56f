# Large Volume Migration Configuration Example
# Handles 500k+ records efficiently

migration:
  name: LargeVolumeAccountMigration
  version: "1.0.0"
  migrationOrder:
    - extractAccounts
    - enrichAccounts
    - loadAccounts
  continueOnError: true
  globalVariables:
    defaultCountry: "US"
    batchDate: "2024-01-15"

connections:
  sourceOrg:
    sfdxAlias: source-org
    apiVersion: "59.0"
  targetOrg:
    sfdxAlias: target-org
    apiVersion: "59.0"

dataSources:
  # Large CSV files - will be processed in streaming fashion
  accountsCsv:
    type: csv
    directory: ./data/accounts
    filePattern: "accounts_batch_*.csv"
    sortFiles: true
    batchSize: 5000  # Smaller batches for memory efficiency
    splitFilesIntoJobs: true  # Each file gets its own job
    addFileMetadata: true
    
  # Reference data for lookups (smaller dataset)
  userMapping:
    type: csv
    filePath: ./data/reference/user_mapping.csv
    batchSize: 10000
    
  # Industry codes from Salesforce (API source)
  industryCodes:
    type: api
    object: Industry_Code__c
    fields:
      - Id
      - Name
      - Code__c
      - Category__c
    connection: sourceOrg

flows:
  extractAccounts:
    name: Extract and Validate Accounts
    description: Extract accounts from multiple CSV files with validation
    sources:
      - name: accountsCsv
        alias: accounts
    transform:
      type: simple
      mappings:
        # Clean and validate data
        AccountNumber: AccountNumber
        Name: $record.Name ? record.Name.trim().substring(0, 255) : 'Unknown'
        BillingStreet: $record.BillingStreet ? record.BillingStreet.replace(/\r\n/g, '\n').substring(0, 255) : null
        BillingCity: BillingCity
        BillingState: BillingState
        BillingPostalCode: $record.BillingPostalCode ? record.BillingPostalCode.replace(/[^0-9-]/g, '') : null
        BillingCountry: $record.BillingCountry || globalVariables.defaultCountry
        Phone: $record.Phone ? record.Phone.replace(/[^0-9+()-.\s]/g, '').substring(0, 40) : null
        Website: $record.Website ? (record.Website.startsWith('http') ? record.Website : 'https://' + record.Website) : null
        AnnualRevenue: $record.AnnualRevenue ? parseFloat(record.AnnualRevenue.replace(/[^0-9.]/g, '')) : null
        NumberOfEmployees: $record.NumberOfEmployees ? parseInt(record.NumberOfEmployees) : null
        Type: $record.Type || 'Prospect'
        Industry: Industry
        Description: $record.Description ? record.Description.substring(0, 32000) : null
        # Add metadata
        Migration_Batch__c: $globalVariables.batchDate
        Source_File__c: $record.__fileName || 'Unknown'
        Source_Record_Index__c: $record.__recordIndex || 0
      filter: $record.Name && record.Name.trim().length > 0
    targets:
      - name: validatedAccounts
        object: Account_Staging__c
        connection: targetOrg
        operation: insert
        loadStrategy: bulk2
        batchSize: 10000
        maxRecordsPerJob: 150000  # Split large files into multiple jobs
    debug:
      saveTransformerCsv: true  # Save validated records

  enrichAccounts:
    name: Enrich Accounts with Reference Data
    description: Add user mappings and industry codes
    sources:
      - name: accountsCsv
        alias: accounts
      - name: userMapping
        alias: users
      - name: industryCodes
        alias: industries
    dependencies:
      - extractAccounts
    transform:
      type: lookup
      sourceAlias: users
      conditions:
        - type: equals
          sourceField: OwnerEmail
          lookupField: Email
      resultField: OwnerId
      multipleMatchStrategy: first
      noMatchStrategy: null
      resultMappings:
        Owner_Name__c: Name
        Owner_Department__c: Department
      mappings:
        "*": "*"  # Keep all existing fields
        Industry_Code__c: $record.Industry ? record.Industry.toUpperCase() : 'OTHER'
        Enrichment_Date__c: $new Date().toISOString()
    targets:
      - name: enrichedAccounts  
        object: Account_Staging__c
        connection: targetOrg
        operation: update
        loadStrategy: bulk2
        externalId: Source_Id__c
        maxRecordsPerJob: 150000

  loadAccounts:
    name: Load Accounts to Production
    description: Final load with deduplication
    sources:
      - name: accountsCsv
        alias: accounts
    dependencies:
      - enrichAccounts
    transform:
      type: aggregation
      groupBy:
        - AccountNumber
      aggregates:
        - field: AnnualRevenue
          operation: max
          targetField: AnnualRevenue
        - field: NumberOfEmployees
          operation: max
          targetField: NumberOfEmployees
        - field: CreatedDate
          operation: min
          targetField: First_Seen_Date__c
      mappings:
        "*": "*"
        Deduped__c: "true"
        Load_Date__c: $new Date().toISOString()
    targets:
      - name: productionAccounts
        object: Account
        connection: targetOrg
        operation: upsert
        externalId: AccountNumber
        loadStrategy: bulk2
        maxRecordsPerJob: 100000  # Smaller batches for production
        bulkPollIntervalMs: 15000  # Check every 15 seconds
        bulkPollTimeoutMs: 3600000  # 1 hour timeout
      # Also export results for audit
      - name: auditExport
        object: Account
        connection: targetOrg
        operation: insert
        loadStrategy: csv-export
        filePath: ./staging/audit/loaded_accounts_${timestamp}.csv

# Debug settings for monitoring
debug:
  saveExtractorCsv: true
  saveTransformerCsv: true
