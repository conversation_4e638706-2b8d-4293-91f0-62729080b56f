import { FlowConfig, MigrationConfig } from "../config/schema";
import { JobContext, FlowResult } from "../core/interfaces";
import { JoinTransformer } from "../transformers/joinTransformer";
import { Logger } from "../utils/logger";

/**
 * Memory-efficient flow execution methods for the orchestrator
 * These should replace the existing methods in orchestrator.ts
 */
export class MemoryEfficientFlowExecutors {
    constructor(private logger: Logger) {}

    /**
     * Execute a join flow without accumulating all records in memory
     */
    async executeJoinFlowEfficient(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, any>,
        extractorFactory: any,
        transformerFactory: any,
        loaderFactory: any
    ): Promise<FlowResult> {
        const joinTransformConfig = flowConfig.transform;
        if (joinTransformConfig.type !== 'join') {
            throw new Error("Invalid transform for executeJoinFlow");
        }

        // Create extractors
        const extractors = await extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        // Create transformer
        const transformer = transformerFactory.createTransformer(
            joinTransformConfig,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        ) as JoinTransformer;

        // Create loaders
        const loaders = await loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        // Load secondary data sources into transformer (these are typically smaller reference data)
        for (const [alias, extractor] of extractors) {
            if (alias !== joinTransformConfig.primarySource) {
                this.logger.info(`Loading reference data for alias: ${alias}`);
                const referenceData: Record<string, any>[] = [];
                
                // Load reference data with a reasonable limit
                const maxReferenceRecords = 100000; // Configure based on available memory
                let recordCount = 0;
                
                for await (const batch of extractor.extract()) {
                    for (const record of batch) {
                        if (recordCount >= maxReferenceRecords) {
                            this.logger.warn(`Reference data ${alias} exceeds ${maxReferenceRecords} records, truncating`);
                            break;
                        }
                        referenceData.push(record);
                        recordCount++;
                    }
                    if (recordCount >= maxReferenceRecords) break;
                }
                
                transformer.setSourceData(alias, referenceData);
                context.flowOutputs.set(alias, referenceData);
            }
        }

        // Process primary source in streaming fashion
        const primaryExtractor = extractors.get(joinTransformConfig.primarySource);
        if (!primaryExtractor) {
            throw new Error(`Primary extractor '${joinTransformConfig.primarySource}' not found`);
        }

        let totalRecords = 0;
        const allErrors: any[] = [];
        const outputs: Record<string, any[]> = {};

        // Initialize outputs for each target
        for (const target of flowConfig.targets) {
            outputs[target.name] = [];
        }

        try {
            // Process primary data in batches
            for await (const batch of primaryExtractor.extract()) {
                // Transform batch
                const transformedBatch = await transformer.transform(batch);
                
                // Load to targets
                for (const targetConfig of flowConfig.targets) {
                    const loader = loaders.get(targetConfig.name);
                    if (!loader) continue;
                    
                    const loadResult = await loader.load(transformedBatch);
                    
                    if (loadResult.errors && loadResult.errors.length > 0) {
                        allErrors.push(...loadResult.errors);
                    }
                    
                    // Don't accumulate output records to save memory
                    // Just track count
                    totalRecords += transformedBatch.length;
                }
                
                this.logger.logBatchProgress(
                    `Join flow ${flowConfig.name}`,
                    totalRecords,
                    totalRecords
                );
            }

            // Finalize loaders
            for (const [targetName, loader] of loaders) {
                if (loader.finalize) {
                    const finalResult = await loader.finalize();
                    if (finalResult.errors && finalResult.errors.length > 0) {
                        allErrors.push(...finalResult.errors);
                    }
                }
            }

            return {
                flowName: flowConfig.name,
                success: allErrors.length === 0,
                recordsProcessed: totalRecords,
                outputs: outputs,
                errors: allErrors,
                duration: 0,
            };

        } catch (error) {
            this.logger.error(`Join flow execution failed: ${flowConfig.name}`, error);
            return {
                flowName: flowConfig.name,
                success: false,
                recordsProcessed: totalRecords,
                outputs: {},
                errors: [error],
                duration: 0,
            };
        }
    }

    /**
     * Execute aggregation flow with memory-efficient processing
     */
    async executeAggregationFlowEfficient(
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        currentDataSources: Record<string, any>,
        extractorFactory: any,
        transformerFactory: any,
        loaderFactory: any
    ): Promise<FlowResult> {
        // For aggregation, we need all records, but we can process in chunks
        // and merge results if the aggregation logic allows it
        
        const extractors = await extractorFactory.createMultipleExtractors(
            flowConfig.sources,
            currentDataSources,
            migrationConfig.connections,
            context,
            context.recordLimit,
            flowConfig.debug
        );

        const primarySourceConfig = flowConfig.sources[0];
        const primaryExtractorAlias = primarySourceConfig.alias || primarySourceConfig.name;
        const primaryExtractor = extractors.get(primaryExtractorAlias);
        
        if (!primaryExtractor) {
            throw new Error(`Primary extractor '${primaryExtractorAlias}' not found`);
        }

        // For aggregation, we might need to accumulate data
        // Consider using a temporary file or database for very large datasets
        const aggregationChunkSize = 100000; // Process in 100k chunks
        const recordChunks: Record<string, any>[][] = [];
        let currentChunk: Record<string, any>[] = [];

        // Collect records in manageable chunks
        for await (const batch of primaryExtractor.extract()) {
            for (const record of batch) {
                currentChunk.push(record);
                
                if (currentChunk.length >= aggregationChunkSize) {
                    recordChunks.push(currentChunk);
                    currentChunk = [];
                    
                    // If we have too many chunks, process them
                    if (recordChunks.length >= 5) {
                        // Process and merge chunks
                        await this.processAggregationChunks(
                            recordChunks,
                            flowConfig,
                            migrationConfig,
                            context,
                            transformerFactory,
                            loaderFactory
                        );
                        recordChunks.length = 0;
                    }
                }
            }
        }

        // Add remaining records
        if (currentChunk.length > 0) {
            recordChunks.push(currentChunk);
        }

        // Process final chunks
        return await this.processAggregationChunks(
            recordChunks,
            flowConfig,
            migrationConfig,
            context,
            transformerFactory,
            loaderFactory
        );
    }

    private async processAggregationChunks(
        chunks: Record<string, any>[][],
        flowConfig: FlowConfig,
        migrationConfig: MigrationConfig,
        context: JobContext,
        transformerFactory: any,
        loaderFactory: any
    ): Promise<FlowResult> {
        // This is a simplified example - real implementation would need
        // to handle merging of aggregated results properly
        
        const transformer = transformerFactory.createTransformer(
            flowConfig.transform,
            {
                ...context,
                globalVariables: migrationConfig.migration.globalVariables ?? {},
            },
            flowConfig.debug
        );

        const loaders = await loaderFactory.createMultipleLoaders(
            flowConfig.targets,
            migrationConfig.connections
        );

        let totalRecords = 0;
        const allErrors: any[] = [];

        // Process each chunk
        for (const chunk of chunks) {
            const transformedRecords = await transformer.transform(chunk);
            totalRecords += chunk.length;

            // Load results
            for (const targetConfig of flowConfig.targets) {
                const loader = loaders.get(targetConfig.name);
                if (!loader) continue;
                
                const loadResult = await loader.load(transformedRecords);
                if (loadResult.errors && loadResult.errors.length > 0) {
                    allErrors.push(...loadResult.errors);
                }
            }
        }

        // Finalize loaders
        for (const [targetName, loader] of loaders) {
            if (loader.finalize) {
                const finalResult = await loader.finalize();
                if (finalResult.errors && finalResult.errors.length > 0) {
                    allErrors.push(...finalResult.errors);
                }
            }
        }

        return {
            flowName: flowConfig.name,
            success: allErrors.length === 0,
            recordsProcessed: totalRecords,
            outputs: {},
            errors: allErrors,
            duration: 0,
        };
    }
}
