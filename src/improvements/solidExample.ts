/**
 * Example demonstrating SOLID principles in the enhanced ETL architecture
 * This file shows how to use the new streaming components with dependency injection
 */

import { Logger } from '../utils/logger';
import { 
    createEnhancedEtlOrchestrator,
    StreamingComponentBuilder,
    FlowExecutionStrategyFactory,
    AugmentContextEngine,
    DefaultStreamingComponentFactory,
    ComponentFactoryRegistry,
    FactoryManager,
    StreamingContextManager,
    FlowExecutionStrategy,
    StreamingComponentFactory
} from './index';

/**
 * Example 1: Basic usage with default components
 * Demonstrates Dependency Inversion Principle - using abstractions
 */
export async function basicStreamingExample(): Promise<void> {
    console.log('🔧 Basic Streaming Example');
    
    // Create orchestrator with default components
    const orchestrator = createEnhancedEtlOrchestrator('info', {
        enablePersistence: true,
        strategyType: 'simple',
        maxMemoryRecords: 50000
    });

    // The orchestrator uses dependency injection internally
    const contextManager = orchestrator.getContextManager();
    const activeContexts = orchestrator.getActiveContexts();
    
    console.log(`Active contexts: ${activeContexts.length}`);
    console.log('✅ Basic example completed');
}

/**
 * Example 2: Advanced usage with custom components
 * Demonstrates Open/Closed Principle - extending without modifying
 */
export async function advancedStreamingExample(): Promise<void> {
    console.log('🚀 Advanced Streaming Example');
    
    const logger = new Logger('debug');
    
    // Create custom components using builder pattern
    const contextManager = new StreamingComponentBuilder()
        .withLogger(logger)
        .withContextOptions({
            enablePersistence: true,
            tempDirectory: './custom-temp',
            maxMemoryRecords: 100000
        })
        .buildContextManager();

    // Create custom execution strategy
    const executionStrategy = FlowExecutionStrategyFactory.create(
        'memory-aware',
        logger
    );

    // Create component factory
    const componentFactory = new DefaultStreamingComponentFactory(logger);

    // Create orchestrator with custom components (Dependency Injection)
    const orchestrator = createEnhancedEtlOrchestrator('debug', {
        enablePersistence: true,
        strategyType: 'memory-aware',
        maxMemoryRecords: 100000
    });

    console.log('✅ Advanced example with custom components completed');
}

/**
 * Example 3: Factory pattern usage
 * Demonstrates Factory Pattern and Registry Pattern
 */
export async function factoryPatternExample(): Promise<void> {
    console.log('🏭 Factory Pattern Example');
    
    const logger = new Logger('info');
    
    // Initialize factory manager (Singleton Pattern)
    FactoryManager.getInstance().initialize(logger);
    
    // Register custom factory
    const customFactory = new DefaultStreamingComponentFactory(logger);
    ComponentFactoryRegistry.register('custom', customFactory);
    
    // Use registered factory
    const factory = ComponentFactoryRegistry.get('custom');
    
    // Create components using factory
    const contextManager = factory.createContextManager({
        enablePersistence: true,
        maxMemoryRecords: 75000
    });

    console.log(`Factory created context manager: ${contextManager.constructor.name}`);
    console.log('✅ Factory pattern example completed');
}

/**
 * Example 4: Strategy pattern for different execution strategies
 * Demonstrates Strategy Pattern
 */
export async function strategyPatternExample(): Promise<void> {
    console.log('📋 Strategy Pattern Example');
    
    const logger = new Logger('info');
    
    // Create different strategies
    const strategies = [
        FlowExecutionStrategyFactory.create('simple', logger),
        FlowExecutionStrategyFactory.create('memory-aware', logger),
        FlowExecutionStrategyFactory.create('parallel', logger, { maxConcurrency: 2 })
    ];

    // Each strategy implements the same interface but with different behavior
    strategies.forEach((strategy, index) => {
        console.log(`Strategy ${index + 1}: ${strategy.constructor.name}`);
    });

    console.log('✅ Strategy pattern example completed');
}

/**
 * Example 5: Demonstrating Single Responsibility Principle
 * Each component has a single, well-defined responsibility
 */
export async function singleResponsibilityExample(): Promise<void> {
    console.log('📝 Single Responsibility Example');
    
    const logger = new Logger('info');
    
    // Context Manager - responsible only for context management
    const contextManager: StreamingContextManager = new AugmentContextEngine(logger);
    
    // Component Factory - responsible only for creating components
    const componentFactory: StreamingComponentFactory = new DefaultStreamingComponentFactory(logger);
    
    // Execution Strategy - responsible only for execution logic
    const executionStrategy: FlowExecutionStrategy = FlowExecutionStrategyFactory.create('simple', logger);

    // Each component has a single responsibility
    console.log('Context Manager responsibility: Context lifecycle management');
    console.log('Component Factory responsibility: Component creation');
    console.log('Execution Strategy responsibility: Flow execution logic');
    
    console.log('✅ Single responsibility example completed');
}

/**
 * Example 6: Interface Segregation Principle
 * Clients depend only on interfaces they use
 */
export async function interfaceSegregationExample(): Promise<void> {
    console.log('🔌 Interface Segregation Example');
    
    const logger = new Logger('info');
    const contextManager = new AugmentContextEngine(logger);
    
    // Client only needs memory monitoring
    function monitorMemory(monitor: { getMemoryStats(jobName: string): any }) {
        const stats = monitor.getMemoryStats('test-job');
        console.log(`Memory usage: ${stats.memoryUsageMB}MB`);
    }
    
    // Client only needs persistence
    function persistData(persistence: { cleanup(jobName: string): Promise<void> }) {
        return persistence.cleanup('test-job');
    }
    
    // Client only needs snapshots
    function createSnapshot(snapshots: { createSnapshot(jobName: string): any }) {
        return snapshots.createSnapshot('test-job');
    }
    
    // Each function depends only on the interface it needs
    monitorMemory(contextManager);
    await persistData(contextManager);
    createSnapshot(contextManager);
    
    console.log('✅ Interface segregation example completed');
}

/**
 * Example 7: Dependency Inversion Principle
 * High-level modules don't depend on low-level modules
 */
export async function dependencyInversionExample(): Promise<void> {
    console.log('🔄 Dependency Inversion Example');
    
    const logger = new Logger('info');
    
    // High-level orchestrator depends on abstractions (interfaces)
    // not concrete implementations
    class CustomOrchestrator {
        constructor(
            private contextManager: StreamingContextManager,
            private executionStrategy: FlowExecutionStrategy,
            private componentFactory: StreamingComponentFactory
        ) {}
        
        async process(): Promise<void> {
            // Uses abstractions, not concrete classes
            const context = this.contextManager.createStreamContext('custom-job', {});
            console.log('Processing with injected dependencies...');
        }
    }
    
    // Inject dependencies (abstractions)
    const contextManager: StreamingContextManager = new AugmentContextEngine(logger);
    const executionStrategy: FlowExecutionStrategy = FlowExecutionStrategyFactory.create('simple', logger);
    const componentFactory: StreamingComponentFactory = new DefaultStreamingComponentFactory(logger);
    
    const customOrchestrator = new CustomOrchestrator(
        contextManager,
        executionStrategy,
        componentFactory
    );
    
    await customOrchestrator.process();
    
    console.log('✅ Dependency inversion example completed');
}

/**
 * Run all SOLID examples
 */
export async function runAllSOLIDExamples(): Promise<void> {
    console.log('🎯 Running all SOLID Principles Examples\n');
    
    try {
        await basicStreamingExample();
        console.log('');
        
        await advancedStreamingExample();
        console.log('');
        
        await factoryPatternExample();
        console.log('');
        
        await strategyPatternExample();
        console.log('');
        
        await singleResponsibilityExample();
        console.log('');
        
        await interfaceSegregationExample();
        console.log('');
        
        await dependencyInversionExample();
        console.log('');
        
        console.log('🎉 All SOLID examples completed successfully!');
        
    } catch (error) {
        console.error('❌ Error running SOLID examples:', error);
        throw error;
    }
}

/**
 * Example showing DRY principle - Don't Repeat Yourself
 */
export class StreamingConfigurationManager {
    private static defaultConfig = {
        batchSize: 10000,
        maxMemoryRecords: 100000,
        enablePersistence: true,
        tempDirectory: './temp'
    };

    // DRY: Common configuration logic in one place
    static createConfig(overrides: Partial<typeof StreamingConfigurationManager.defaultConfig> = {}) {
        return { ...this.defaultConfig, ...overrides };
    }

    // DRY: Reusable validation logic
    static validateConfig(config: any): string[] {
        const errors: string[] = [];
        
        if (config.batchSize <= 0) {
            errors.push('Batch size must be positive');
        }
        
        if (config.maxMemoryRecords <= 0) {
            errors.push('Max memory records must be positive');
        }
        
        return errors;
    }

    // DRY: Common orchestrator creation logic
    static createOrchestrator(config: any) {
        const validationErrors = this.validateConfig(config);
        if (validationErrors.length > 0) {
            throw new Error(`Configuration errors: ${validationErrors.join(', ')}`);
        }
        
        return createEnhancedEtlOrchestrator('info', config);
    }
}

// Export for CLI usage
if (require.main === module) {
    runAllSOLIDExamples().catch(console.error);
}
