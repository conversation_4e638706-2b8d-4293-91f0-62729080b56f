import * as jsforce from "jsforce";
import * as fs from "fs";
import * as path from "path";
import { Loader, LoadResult } from "../core/interfaces";
import { TargetConfig } from "../config/schema";
import { ConnectionService } from "../services/connectionService";
import { Logger } from "../utils/logger";
import { CsvHelper } from "../utils/csvHelper";
import { StreamingCsvHelper } from "./streamingCsvHelper";

/**
 * Streaming Bulk API 2.0 loader that processes data in chunks
 * Handles large volumes without memory overflow
 */
export class StreamingBulkLoader implements Loader {
    private connection: jsforce.Connection | null = null;
    private tempCsvPath: string;
    private recordCount: number = 0;
    private csvWriteStream: fs.WriteStream | null = null;
    private isFirstBatch: boolean = true;
    private jobResults: LoadResult[] = [];

    constructor(
        private config: TargetConfig,
        private connectionService: ConnectionService,
        private connectionName: string,
        private logger: Logger,
        private maxRecordsPerJob: number = 150000 // Salesforce recommends max 150k per job
    ) {
        // Create temp CSV file for accumulating records
        const tempDir = path.join(process.cwd(), 'staging', 'temp');
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        this.tempCsvPath = path.join(tempDir, `bulk_${Date.now()}_${config.object}.csv`);
    }

    async load(records: Record<string, any>[]): Promise<LoadResult> {
        if (records.length === 0) {
            return {
                recordsProcessed: 0,
                recordsSucceeded: 0,
                recordsFailed: 0,
                errors: [],
                duration: 0,
                success: true,
            };
        }

        // Initialize CSV write stream if needed
        if (!this.csvWriteStream) {
            this.csvWriteStream = fs.createWriteStream(this.tempCsvPath, { flags: 'a' });
        }

        // Write records to temp CSV file
        const csvContent = CsvHelper.convertToCsv(records, this.isFirstBatch);
        await new Promise<void>((resolve, reject) => {
            this.csvWriteStream!.write(csvContent, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });

        this.isFirstBatch = false;
        this.recordCount += records.length;

        // If we've reached the max records per job, process this batch
        if (this.recordCount >= this.maxRecordsPerJob) {
            await this.processBulkJob();
        }

        return {
            recordsProcessed: records.length,
            recordsSucceeded: records.length,
            recordsFailed: 0,
            errors: [],
            duration: 0,
            success: true,
        };
    }

    async finalize(): Promise<LoadResult> {
        // Process any remaining records
        if (this.recordCount > 0) {
            await this.processBulkJob();
        }

        // Clean up
        if (this.csvWriteStream) {
            this.csvWriteStream.end();
        }
        if (fs.existsSync(this.tempCsvPath)) {
            fs.unlinkSync(this.tempCsvPath);
        }

        // Aggregate results from all jobs
        const totalProcessed = this.jobResults.reduce((sum, r) => sum + r.recordsProcessed, 0);
        const totalSucceeded = this.jobResults.reduce((sum, r) => sum + r.recordsSucceeded, 0);
        const totalFailed = this.jobResults.reduce((sum, r) => sum + r.recordsFailed, 0);
        const allErrors = this.jobResults.flatMap(r => r.errors);
        const totalDuration = this.jobResults.reduce((sum, r) => sum + (r.duration || 0), 0);
        const allSuccess = this.jobResults.every(r => r.success);

        this.logger.info(`Bulk operation completed: ${this.jobResults.length} jobs, ${totalSucceeded} successful, ${totalFailed} failed`);

        return {
            recordsProcessed: totalProcessed,
            recordsSucceeded: totalSucceeded,
            recordsFailed: totalFailed,
            errors: allErrors,
            duration: totalDuration,
            success: allSuccess,
        };
    }

    private async processBulkJob(): Promise<void> {
        if (!this.connection) {
            await this.initializeConnection();
        }

        // Close the write stream to ensure all data is flushed
        if (this.csvWriteStream) {
            this.csvWriteStream.end();
            this.csvWriteStream = null;
        }

        const startTime = Date.now();
        const jobTimestamp = new Date().toISOString().replace(/[:.]/g, '-');

        try {
            this.logger.info(`Starting Bulk API 2.0 job: ${this.recordCount} records to ${this.config.object}`);

            // Create job options
            const jobOptions: any = {
                object: this.config.object,
                operation: this.config.operation,
                lineEnding: "LF",
            };

            if (this.config.operation === "upsert" && this.config.externalId) {
                jobOptions.externalIdFieldName = this.config.externalId;
            }

            // Create and execute bulk job
            const job = this.connection!.bulk2.createJob(jobOptions);
            const jobInfo = await job.open();

            this.logger.info(`Bulk job opened: ${job.id}`);

            // Save a copy of the CSV being uploaded for auditing
            const auditDir = path.join(process.cwd(), 'staging', 'audit', jobInfo.id);
            fs.mkdirSync(auditDir, { recursive: true });
            const auditCsvPath = path.join(auditDir, `uploaded_${this.config.object}_${jobTimestamp}.csv`);
            fs.copyFileSync(this.tempCsvPath, auditCsvPath);

            // Upload CSV file
            const csvStream = fs.createReadStream(this.tempCsvPath);
            await job.uploadData(csvStream);
            this.logger.info(`Data uploaded to bulk job ${job.id}`);

            // Close job and poll for completion
            await job.close();
            await job.poll(
                this.config.bulkPollIntervalMs || 10000, 
                this.config.bulkPollTimeoutMs || 1200000
            );

            // Get results
            const results = await job.getAllResults();
            const successfulResults = results.successfulResults || [];
            const failedResults = results.failedResults || [];
            const unprocessedRecords = Array.isArray(results.unprocessedRecords)
                ? results.unprocessedRecords
                : results.unprocessedRecords
                ? [results.unprocessedRecords]
                : [];

            // Save results to CSV for auditing
            await this.saveBulkResults(
                job.id,
                successfulResults,
                failedResults,
                unprocessedRecords,
                jobTimestamp
            );

            const duration = Date.now() - startTime;
            const finalJobInfo = job.getInfo();
            const successCount = finalJobInfo.numberRecordsProcessed - finalJobInfo.numberRecordsFailed;
            const failureCount = finalJobInfo.numberRecordsFailed || 0;

            const jobResult: LoadResult = {
                recordsProcessed: this.recordCount,
                recordsSucceeded: successCount,
                recordsFailed: failureCount,
                errors: failedResults.map((r: any) => ({
                    id: r.sf__Id,
                    error: r.sf__Error || 'Unknown error'
                })),
                duration,
                success: failureCount === 0,
            };

            this.jobResults.push(jobResult);

            // Reset for next job
            this.recordCount = 0;
            this.isFirstBatch = true;
            fs.unlinkSync(this.tempCsvPath);
            
            // Create new temp file for next batch
            this.tempCsvPath = path.join(
                path.dirname(this.tempCsvPath), 
                `bulk_${Date.now()}_${this.config.object}.csv`
            );

        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error("Bulk operation failed", error);

            this.jobResults.push({
                recordsProcessed: this.recordCount,
                recordsSucceeded: 0,
                recordsFailed: this.recordCount,
                errors: [{
                    message: error instanceof Error ? error.message : String(error)
                }],
                duration,
                success: false,
            });

            // Reset for next job
            this.recordCount = 0;
            this.isFirstBatch = true;
        }
    }

    private async initializeConnection(): Promise<void> {
        if (!this.connection) {
            this.connection = await this.connectionService.getConnection(
                this.connectionName
            );
        }
    }

    private async saveBulkResults(
        jobId: string,
        successfulResults: any[],
        failedResults: any[],
        unprocessedRecords: any[],
        jobTimestamp: string
    ): Promise<void> {
        try {
            const resultsDir = path.join(
                process.cwd(),
                "staging",
                "job_results",
                jobId
            );
            fs.mkdirSync(resultsDir, { recursive: true });

            // Save successful results using streaming for large datasets
            if (successfulResults.length > 0) {
                const successFilePath = path.join(
                    resultsDir,
                    `successful_records_${jobTimestamp}.csv`
                );
                await StreamingCsvHelper.writeRecordsStreaming(
                    successfulResults,
                    successFilePath,
                    10000 // Batch size for writing
                );
                this.logger.info(
                    `Saved ${successfulResults.length} successful records to ${successFilePath}`
                );
            }

            // Save failed results
            if (failedResults.length > 0) {
                const failureFilePath = path.join(
                    resultsDir,
                    `failed_records_${jobTimestamp}.csv`
                );
                await CsvHelper.writeToCsv(failedResults, failureFilePath);
                this.logger.info(
                    `Saved ${failedResults.length} failed records to ${failureFilePath}`
                );
            }

            // Save unprocessed records
            if (unprocessedRecords.length > 0) {
                const unprocessedFilePath = path.join(
                    resultsDir,
                    `unprocessed_records_${jobTimestamp}.csv`
                );
                await CsvHelper.writeToCsv(
                    unprocessedRecords,
                    unprocessedFilePath
                );
                this.logger.info(
                    `Saved ${unprocessedRecords.length} unprocessed records to ${unprocessedFilePath}`
                );
            }

            // Create a summary file
            const summaryPath = path.join(resultsDir, `job_summary_${jobTimestamp}.json`);
            const summary = {
                jobId,
                timestamp: jobTimestamp,
                object: this.config.object,
                operation: this.config.operation,
                totalRecords: successfulResults.length + failedResults.length + unprocessedRecords.length,
                successfulRecords: successfulResults.length,
                failedRecords: failedResults.length,
                unprocessedRecords: unprocessedRecords.length,
            };
            fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

        } catch (error) {
            this.logger.error(
                `Failed to save bulk results for job ${jobId}`,
                error
            );
        }
    }
}
