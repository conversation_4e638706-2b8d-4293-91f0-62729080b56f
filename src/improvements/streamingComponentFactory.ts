import { Logger } from '../utils/logger';
import { 
    StreamingComponentFactory, 
    StreamingExtractor, 
    BatchProcessor, 
    MemoryEfficientLoader,
    StreamingContextManager,
    StreamContextOptions
} from './interfaces';
import { AugmentContextEngine } from './augmentContextEngine';
import { StreamingCsvExtractor } from './streamingCsvExtractor';
import { StreamingTransformerWrapper } from './batchTransformer';
import { EnhancedBulkLoader } from './enhancedBulkLoader';
import { DataSourceConfig } from '../config/schema';
import { Transformer } from '../core/interfaces';

/**
 * Factory for creating streaming components
 * Follows Dependency Inversion Principle: Depends on abstractions, not concretions
 * Follows Single Responsibility Principle: Component creation only
 */
export class DefaultStreamingComponentFactory implements StreamingComponentFactory {
    constructor(
        private logger: Logger,
        private connectionService?: any
    ) {}

    /**
     * Create streaming extractor based on configuration
     * Follows Open/Closed Principle: Extensible for new extractor types
     */
    createExtractor(config: DataSourceConfig): StreamingExtractor<any> {
        switch (config.type) {
            case 'csv':
                return new StreamingCsvExtractor(config, this.logger);
            
            case 'database':
                // Could be extended for database streaming extractors
                throw new Error('Database streaming extractors not yet implemented');
            
            case 'api':
                // Could be extended for API streaming extractors
                throw new Error('API streaming extractors not yet implemented');
            
            default:
                throw new Error(`Unsupported extractor type: ${config.type}`);
        }
    }

    /**
     * Create batch processor (transformer wrapper)
     */
    createTransformer(transformer: Transformer): BatchProcessor<any, any> {
        // Check if transformer already implements BatchProcessor
        if ('transformBatch' in transformer) {
            return transformer as BatchProcessor<any, any>;
        }
        
        // Wrap existing transformer for batch processing
        return new StreamingTransformerWrapper(transformer);
    }

    /**
     * Create memory-efficient loader
     */
    createLoader(config: any): MemoryEfficientLoader<any> {
        if (!this.connectionService) {
            throw new Error('Connection service required for loader creation');
        }
        
        // For now, return enhanced bulk loader
        // Could be extended for other loader types
        return new EnhancedBulkLoader(config, this.connectionService, this.logger);
    }

    /**
     * Create context manager
     */
    createContextManager(options?: StreamContextOptions): StreamingContextManager {
        return new AugmentContextEngine(
            this.logger,
            options?.tempDirectory,
            options?.enablePersistence
        );
    }
}

/**
 * Builder pattern for creating configured streaming components
 * Follows Builder Pattern for complex object construction
 */
export class StreamingComponentBuilder {
    private logger?: Logger;
    private connectionService?: any;
    private contextOptions?: StreamContextOptions;

    withLogger(logger: Logger): StreamingComponentBuilder {
        this.logger = logger;
        return this;
    }

    withConnectionService(connectionService: any): StreamingComponentBuilder {
        this.connectionService = connectionService;
        return this;
    }

    withContextOptions(options: StreamContextOptions): StreamingComponentBuilder {
        this.contextOptions = options;
        return this;
    }

    build(): StreamingComponentFactory {
        if (!this.logger) {
            throw new Error('Logger is required');
        }

        return new DefaultStreamingComponentFactory(this.logger, this.connectionService);
    }

    buildContextManager(): StreamingContextManager {
        if (!this.logger) {
            throw new Error('Logger is required');
        }

        return new AugmentContextEngine(
            this.logger,
            this.contextOptions?.tempDirectory,
            this.contextOptions?.enablePersistence
        );
    }
}

/**
 * Registry for component factories
 * Follows Registry Pattern for managing multiple factory implementations
 */
export class ComponentFactoryRegistry {
    private static factories: Map<string, StreamingComponentFactory> = new Map();

    static register(name: string, factory: StreamingComponentFactory): void {
        this.factories.set(name, factory);
    }

    static get(name: string): StreamingComponentFactory {
        const factory = this.factories.get(name);
        if (!factory) {
            throw new Error(`Factory not found: ${name}`);
        }
        return factory;
    }

    static getDefault(): StreamingComponentFactory {
        return this.get('default');
    }

    static list(): string[] {
        return Array.from(this.factories.keys());
    }

    static clear(): void {
        this.factories.clear();
    }
}

/**
 * Configuration-based factory
 * Follows Configuration Pattern for declarative component creation
 */
export interface ComponentConfiguration {
    extractor: {
        type: string;
        config: any;
    };
    transformer: {
        type: string;
        config: any;
    };
    loader: {
        type: string;
        config: any;
    };
    context?: StreamContextOptions;
}

export class ConfigurableStreamingFactory {
    constructor(
        private baseFactory: StreamingComponentFactory,
        private transformerFactory?: any
    ) {}

    createFromConfiguration(config: ComponentConfiguration): {
        extractor: StreamingExtractor<any>;
        transformer: BatchProcessor<any, any>;
        loader: MemoryEfficientLoader<any>;
        contextManager: StreamingContextManager;
    } {
        // Create extractor
        const extractor = this.baseFactory.createExtractor(config.extractor.config);

        // Create transformer
        let transformer: BatchProcessor<any, any>;
        if (this.transformerFactory) {
            const baseTransformer = this.transformerFactory.create(config.transformer.config);
            transformer = this.baseFactory.createTransformer(baseTransformer);
        } else {
            throw new Error('Transformer factory not provided');
        }

        // Create loader
        const loader = this.baseFactory.createLoader(config.loader.config);

        // Create context manager
        const contextManager = this.baseFactory.createContextManager(config.context);

        return {
            extractor,
            transformer,
            loader,
            contextManager
        };
    }
}

/**
 * Utility functions for factory creation
 */
export class FactoryUtils {
    /**
     * Create a default factory with common configuration
     */
    static createDefaultFactory(logger: Logger, connectionService?: any): StreamingComponentFactory {
        return new DefaultStreamingComponentFactory(logger, connectionService);
    }

    /**
     * Create a factory builder
     */
    static createBuilder(): StreamingComponentBuilder {
        return new StreamingComponentBuilder();
    }

    /**
     * Register default factory in registry
     */
    static registerDefaults(logger: Logger, connectionService?: any): void {
        const defaultFactory = FactoryUtils.createDefaultFactory(logger, connectionService);
        ComponentFactoryRegistry.register('default', defaultFactory);
    }

    /**
     * Validate component configuration
     */
    static validateConfiguration(config: ComponentConfiguration): string[] {
        const errors: string[] = [];

        if (!config.extractor?.type) {
            errors.push('Extractor type is required');
        }

        if (!config.transformer?.type) {
            errors.push('Transformer type is required');
        }

        if (!config.loader?.type) {
            errors.push('Loader type is required');
        }

        return errors;
    }
}

/**
 * Singleton factory manager
 * Follows Singleton Pattern for global factory access
 */
export class FactoryManager {
    private static instance: FactoryManager;
    private factory?: StreamingComponentFactory;

    private constructor() {}

    static getInstance(): FactoryManager {
        if (!FactoryManager.instance) {
            FactoryManager.instance = new FactoryManager();
        }
        return FactoryManager.instance;
    }

    setFactory(factory: StreamingComponentFactory): void {
        this.factory = factory;
    }

    getFactory(): StreamingComponentFactory {
        if (!this.factory) {
            throw new Error('Factory not initialized. Call setFactory() first.');
        }
        return this.factory;
    }

    initialize(logger: Logger, connectionService?: any): void {
        this.factory = FactoryUtils.createDefaultFactory(logger, connectionService);
        FactoryUtils.registerDefaults(logger, connectionService);
    }
}
