import * as fs from "fs";
import * as path from "path";
import csv from "csv-parser";
import { Extractor } from "../core/interfaces";
import { DataSourceConfig } from "../config/schema";
import { Logger } from "../utils/logger";
import { Transform } from "stream";

/**
 * Streaming CSV extractor optimized for large files
 * Uses Node.js streams to process files without loading entire content into memory
 */
export class StreamingCsvExtractor implements Extractor {
    private filePath: string;
    private batchSize: number;
    private recordLimit?: number;
    private headers?: string[];

    constructor(
        private config: DataSourceConfig,
        private logger: Logger,
        recordLimit?: number
    ) {
        if (config.type !== 'csv') {
            throw new Error('StreamingCsvExtractor only supports CSV data sources');
        }

        this.filePath = config.filePath!;
        this.batchSize = config.batchSize || 5000; // Smaller default for streaming
        this.recordLimit = recordLimit;

        if (!fs.existsSync(this.filePath)) {
            throw new Error(`CSV file not found: ${this.filePath}`);
        }

        // Check file size and warn if very large
        const stats = fs.statSync(this.filePath);
        const fileSizeInMB = stats.size / (1024 * 1024);
        
        if (fileSizeInMB > 500) {
            this.logger.warn(`Processing large CSV file: ${fileSizeInMB.toFixed(2)}MB`);
            // Use smaller batch size for very large files
            this.batchSize = Math.min(this.batchSize, 2000);
        }
    }

    /**
     * Extract records using streaming approach
     */
    async *extract(): AsyncGenerator<Record<string, any>[], void, unknown> {
        let recordCount = 0;
        let batch: Record<string, any>[] = [];
        let isFirstBatch = true;

        const stream = fs.createReadStream(this.filePath)
            .pipe(csv({
                skipEmptyLines: true,
                skipLinesWithError: true
            }));

        try {
            for await (const record of stream) {
                // Check global record limit
                if (this.recordLimit && recordCount >= this.recordLimit) {
                    this.logger.info(
                        `Record limit of ${this.recordLimit} reached. Stopping extraction at ${recordCount} records.`
                    );
                    if (batch.length > 0) {
                        yield [...batch];
                    }
                    return;
                }

                // Store headers from first record
                if (isFirstBatch && !this.headers) {
                    this.headers = Object.keys(record);
                    this.logger.debug(`CSV headers detected: ${this.headers.join(', ')}`);
                    isFirstBatch = false;
                }

                // Clean and validate record
                const cleanedRecord = this.cleanRecord(record);
                if (cleanedRecord) {
                    batch.push(cleanedRecord);
                    recordCount++;
                }

                // Yield batch when it reaches the batch size
                if (batch.length >= this.batchSize) {
                    this.logger.debug(`Yielding batch of ${batch.length} records (total: ${recordCount})`);
                    yield [...batch];
                    batch = [];
                }
            }

            // Yield any remaining records
            if (batch.length > 0) {
                this.logger.debug(`Yielding final batch of ${batch.length} records`);
                yield [...batch];
            }

            this.logger.info(`Streaming extraction completed. Total records: ${recordCount}`);

        } catch (error) {
            this.logger.error(`Error during streaming CSV extraction: ${error}`);
            throw error;
        }
    }

    /**
     * Get estimated record count (for progress tracking)
     */
    async getRecordCount(): Promise<number> {
        // For streaming, we estimate based on file size
        // This is approximate and used for progress indication
        const stats = fs.statSync(this.filePath);
        const fileSizeInBytes = stats.size;
        
        // Estimate ~100 bytes per record (very rough estimate)
        const estimatedRecords = Math.floor(fileSizeInBytes / 100);
        
        this.logger.debug(`Estimated record count: ${estimatedRecords}`);
        return estimatedRecords;
    }

    /**
     * Get detected headers
     */
    getHeaders(): string[] | undefined {
        return this.headers;
    }

    /**
     * Clean and validate a record
     */
    private cleanRecord(record: Record<string, any>): Record<string, any> | null {
        const cleaned: Record<string, any> = {};
        let hasValidData = false;

        for (const [key, value] of Object.entries(record)) {
            // Skip empty keys
            if (!key || key.trim() === '') {
                continue;
            }

            // Clean the key
            const cleanKey = key.trim();
            
            // Handle the value
            let cleanValue = value;
            
            if (typeof value === 'string') {
                cleanValue = value.trim();
                
                // Convert empty strings to null
                if (cleanValue === '') {
                    cleanValue = null;
                }
                // Try to parse numbers
                else if (!isNaN(Number(cleanValue)) && cleanValue !== '') {
                    const numValue = Number(cleanValue);
                    if (Number.isFinite(numValue)) {
                        cleanValue = numValue;
                    }
                }
                // Try to parse booleans
                else if (cleanValue.toLowerCase() === 'true') {
                    cleanValue = true;
                } else if (cleanValue.toLowerCase() === 'false') {
                    cleanValue = false;
                }
            }

            cleaned[cleanKey] = cleanValue;
            
            // Check if we have any non-null data
            if (cleanValue !== null && cleanValue !== undefined) {
                hasValidData = true;
            }
        }

        // Return null if the record has no valid data
        return hasValidData ? cleaned : null;
    }

    /**
     * Create a memory-efficient line counter for very large files
     */
    private async countLinesEfficiently(): Promise<number> {
        return new Promise((resolve, reject) => {
            let lineCount = 0;
            const stream = fs.createReadStream(this.filePath);
            
            const lineCounter = new Transform({
                transform(chunk, encoding, callback) {
                    const lines = chunk.toString().split('\n').length - 1;
                    lineCount += lines;
                    callback();
                }
            });

            stream.pipe(lineCounter);
            
            stream.on('end', () => {
                resolve(Math.max(0, lineCount - 1)); // Subtract 1 for header
            });
            
            stream.on('error', reject);
            lineCounter.on('error', reject);
        });
    }

    /**
     * Check if file is suitable for streaming (based on size and format)
     */
    static isStreamingRecommended(filePath: string): boolean {
        try {
            const stats = fs.statSync(filePath);
            const fileSizeInMB = stats.size / (1024 * 1024);
            
            // Recommend streaming for files larger than 50MB
            return fileSizeInMB > 50;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get file statistics
     */
    getFileStats(): {
        sizeInMB: number;
        path: string;
        recommendStreaming: boolean;
    } {
        const stats = fs.statSync(this.filePath);
        const sizeInMB = stats.size / (1024 * 1024);
        
        return {
            sizeInMB,
            path: this.filePath,
            recommendStreaming: StreamingCsvExtractor.isStreamingRecommended(this.filePath)
        };
    }
}
