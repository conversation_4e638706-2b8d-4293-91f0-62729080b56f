import * as fs from 'fs';
import * as path from 'path';
import { Transform } from 'stream';

/**
 * Enhanced CSV Helper with streaming capabilities
 */
export class StreamingCsvHelper {
    /**
     * Convert records to CSV string (in-memory, for small datasets)
     */
    static convertToCsv(records: Record<string, any>[], includeHeader: boolean = true): string {
        if (!records.length) return '';

        const headers = Object.keys(records[0]);
        let csvString = '';

        if (includeHeader) {
            csvString += headers.map(header => this.escapeCsvCell(header)).join(',') + '\n';
        }

        records.forEach(record => {
            csvString += headers.map(header => this.escapeCsvCell(record[header])).join(',') + '\n';
        });

        return csvString;
    }

    /**
     * Write records to CSV using streaming to handle large datasets
     */
    static async writeRecordsStreaming(
        records: Record<string, any>[],
        filePath: string,
        batchSize: number = 10000
    ): Promise<void> {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        const writeStream = fs.createWriteStream(filePath);
        let headerWritten = false;

        return new Promise((resolve, reject) => {
            writeStream.on('error', reject);
            writeStream.on('finish', resolve);

            // Process records in batches
            for (let i = 0; i < records.length; i += batchSize) {
                const batch = records.slice(i, i + batchSize);
                
                if (!headerWritten && batch.length > 0) {
                    const headers = Object.keys(batch[0]);
                    writeStream.write(headers.map(h => this.escapeCsvCell(h)).join(',') + '\n');
                    headerWritten = true;
                }

                batch.forEach(record => {
                    const row = Object.keys(batch[0]).map(key => 
                        this.escapeCsvCell(record[key])
                    ).join(',') + '\n';
                    writeStream.write(row);
                });
            }

            writeStream.end();
        });
    }

    /**
     * Write records to CSV file (small datasets)
     */
    static async writeToCsv(
        records: Record<string, any>[], 
        filePath: string, 
        append: boolean = false
    ): Promise<void> {
        if (!records.length) return;

        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        const csvContent = this.convertToCsv(records, !append || !fs.existsSync(filePath));
        
        if (append) {
            fs.appendFileSync(filePath, csvContent);
        } else {
            fs.writeFileSync(filePath, csvContent);
        }
    }

    /**
     * Create a CSV transform stream for processing records on the fly
     */
    static createCsvTransformStream(includeHeader: boolean = true): Transform {
        let headerWritten = !includeHeader;
        let headers: string[] = [];

        return new Transform({
            objectMode: true,
            transform(record: Record<string, any>, encoding, callback) {
                try {
                    if (!headerWritten) {
                        headers = Object.keys(record);
                        this.push(headers.map(h => StreamingCsvHelper.escapeCsvCell(h)).join(',') + '\n');
                        headerWritten = true;
                    }

                    const row = headers.map(key => 
                        StreamingCsvHelper.escapeCsvCell(record[key])
                    ).join(',') + '\n';
                    
                    callback(null, row);
                } catch (error) {
                    callback(error as Error);
                }
            }
        });
    }

    /**
     * Append records to an existing CSV file efficiently
     */
    static async appendToCsv(
        records: Record<string, any>[],
        filePath: string
    ): Promise<void> {
        if (!records.length) return;

        const fileExists = fs.existsSync(filePath);
        const writeStream = fs.createWriteStream(filePath, { flags: 'a' });

        return new Promise((resolve, reject) => {
            writeStream.on('error', reject);
            writeStream.on('finish', resolve);

            // Write header only if file doesn't exist
            if (!fileExists && records.length > 0) {
                const headers = Object.keys(records[0]);
                writeStream.write(headers.map(h => this.escapeCsvCell(h)).join(',') + '\n');
            }

            // Write records
            records.forEach(record => {
                const headers = Object.keys(fileExists ? record : records[0]);
                const row = headers.map(key => 
                    this.escapeCsvCell(record[key])
                ).join(',') + '\n';
                writeStream.write(row);
            });

            writeStream.end();
        });
    }

    /**
     * Escape CSV cell data according to RFC 4180
     */
    static escapeCsvCell(cellData: any): string {
        if (cellData === null || typeof cellData === 'undefined') return '';
        
        const stringData = String(cellData);
        
        if (stringData.includes(',') || stringData.includes('\n') || stringData.includes('"')) {
            return `"${stringData.replace(/"/g, '""')}"`;
        }
        
        return stringData;
    }

    /**
     * Stream records to CSV file directly from an async generator
     */
    static async streamToCsv(
        generator: AsyncGenerator<Record<string, any>[], void, unknown>,
        filePath: string
    ): Promise<number> {
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        const writeStream = fs.createWriteStream(filePath);
        const csvTransform = this.createCsvTransformStream(true);
        
        csvTransform.pipe(writeStream);

        let totalRecords = 0;

        return new Promise((resolve, reject) => {
            writeStream.on('error', reject);
            csvTransform.on('error', reject);
            writeStream.on('finish', () => resolve(totalRecords));

            (async () => {
                try {
                    for await (const batch of generator) {
                        for (const record of batch) {
                            csvTransform.write(record);
                            totalRecords++;
                        }
                    }
                    csvTransform.end();
                } catch (error) {
                    csvTransform.destroy(error as Error);
                    reject(error);
                }
            })();
        });
    }
}
