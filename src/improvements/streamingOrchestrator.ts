import { EtlOrchestrator } from "../core/orchestrator";
import { Extractor, Transformer, Loader, FlowResult } from "../core/interfaces";
import { StreamingCsvHelper } from "./streamingCsvHelper";
import { BatchTransformer, StreamingTransformerWrapper } from "./batchTransformer";
import * as path from "path";
import * as fs from "fs";

/**
 * Streaming orchestrator that processes data in batches
 * Handles large volumes without memory overflow
 */
export class StreamingOrchestrator extends EtlOrchestrator {
    private readonly DEFAULT_BATCH_SIZE = 10000;
    private readonly MAX_MEMORY_RECORDS = 50000;

    /**
     * Execute core ETL flow with streaming (extract → transform → load)
     */
    protected async executeFlow(
        flowName: string,
        extractor: Extractor,
        transformer: Transformer,
        loader: Loader,
        context: any
    ): Promise<FlowResult> {
        const startTime = Date.now();
        let totalRecords = 0;
        const allResults: any[] = [];
        const allErrors: any[] = [];

        // Wrap transformer if it's not already a batch transformer
        const batchTransformer = this.wrapTransformer(transformer);

        // Initialize transformer if needed
        if (batchTransformer.initialize) {
            await batchTransformer.initialize();
        }

        // Set up CSV saving for transformed data if debug enabled
        const shouldSaveTransformed = context.debugOptions?.saveTransformerCsv;
        let transformedCsvPath: string | null = null;
        if (shouldSaveTransformed) {
            const stagingDir = path.join(process.cwd(), 'staging', 'transformed');
            if (!fs.existsSync(stagingDir)) {
                fs.mkdirSync(stagingDir, { recursive: true });
            }
            transformedCsvPath = path.join(
                stagingDir, 
                `${flowName}_transformed_${Date.now()}.csv`
            );
        }

        try {
            // Process in streaming batches
            for await (const extractedBatch of extractor.extract()) {
                // Transform batch
                const transformedBatch = await batchTransformer.transformBatch(extractedBatch);

                // Save transformed data if debug enabled
                if (transformedCsvPath && transformedBatch.length > 0) {
                    await StreamingCsvHelper.appendToCsv(transformedBatch, transformedCsvPath);
                }

                // Load batch
                const loadResult = await loader.load(transformedBatch);

                totalRecords += transformedBatch.length;

                if (!loader.finalize) {
                    allResults.push(...(loadResult.results ?? []));
                    allErrors.push(...loadResult.errors);
                }

                this.logger.logBatchProgress(
                    `${flowName} processing`,
                    totalRecords,
                    totalRecords
                );
            }

            // Finalize transformer if needed
            if (batchTransformer.finalize) {
                const finalRecords = await batchTransformer.finalize();
                if (finalRecords.length > 0) {
                    // Save final transformed records
                    if (transformedCsvPath) {
                        await StreamingCsvHelper.appendToCsv(finalRecords, transformedCsvPath);
                    }

                    // Load final records
                    const loadResult = await loader.load(finalRecords);
                    totalRecords += finalRecords.length;

                    if (!loader.finalize) {
                        allResults.push(...(loadResult.results ?? []));
                        allErrors.push(...loadResult.errors);
                    }
                }
            }

            // Finalize loader
            if (loader.finalize) {
                const finalizeResult = await loader.finalize();
                allResults.push(...(finalizeResult.results ?? []));
                allErrors.push(...finalizeResult.errors);
            }

            const duration = Date.now() - startTime;
            const success = allErrors.length === 0;

            this.logger.info(`Flow ${flowName} completed: ${totalRecords} records processed in ${duration}ms`);
            if (transformedCsvPath) {
                this.logger.info(`Transformed data saved to: ${transformedCsvPath}`);
            }

            return {
                flowName,
                success,
                recordsProcessed: totalRecords,
                duration,
                outputs: {
                    default: allResults,
                },
                errors: allErrors,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error(`Flow execution failed: ${flowName}`, error);

            return {
                flowName,
                success: false,
                recordsProcessed: totalRecords,
                duration,
                outputs: {},
                errors: [
                    {
                        message:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    },
                ],
            };
        }
    }

    /**
     * Wrap a regular transformer to support batch processing
     */
    private wrapTransformer(transformer: Transformer): BatchTransformer {
        // Check if it's already a batch transformer
        if ('transformBatch' in transformer) {
            return transformer as BatchTransformer;
        }

        // Wrap it for streaming
        return new StreamingTransformerWrapper(transformer, this.MAX_MEMORY_RECORDS);
    }

    /**
     * Override to use streaming CSV extractor for large files
     */
    protected createCsvExtractor(config: any, logger: any, recordLimit?: number): Extractor {
        // For files larger than 100MB, use streaming approach
        const stats = fs.statSync(config.filePath);
        const fileSizeInMB = stats.size / (1024 * 1024);

        if (fileSizeInMB > 100) {
            logger.info(`Using streaming CSV extractor for large file: ${fileSizeInMB.toFixed(2)}MB`);
            // Would create a streaming CSV extractor here
            // For now, return the regular one with smaller batch size
            config.batchSize = Math.min(config.batchSize || 5000, 5000);
        }

        return super.createCsvExtractor(config, logger, recordLimit);
    }
}
