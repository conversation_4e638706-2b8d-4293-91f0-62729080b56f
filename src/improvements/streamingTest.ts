import { createEnhancedEtlOrchestrator } from './index';
import { AugmentContextEngine } from './augmentContextEngine';
import { StreamingCsvExtractor } from './streamingCsvExtractor';
import { StreamingCsvHelper } from './streamingCsvHelper';
import { Logger } from '../utils/logger';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Test suite for streaming ETL functionality
 * Validates AugmentContextEngine and streaming components
 */
export class StreamingETLTest {
    private logger: Logger;
    private testDataDir: string;

    constructor() {
        this.logger = new Logger('debug');
        this.testDataDir = path.join(process.cwd(), 'test-data');
        this.ensureTestDataDir();
    }

    /**
     * Run all streaming tests
     */
    async runAllTests(): Promise<void> {
        this.logger.info('Starting streaming ETL tests...');

        try {
            await this.testAugmentContextEngine();
            await this.testStreamingCsvExtractor();
            await this.testStreamingCsvHelper();
            await this.testEnhancedOrchestrator();
            await this.testMemoryManagement();

            this.logger.info('All streaming tests completed successfully!');
        } catch (error) {
            this.logger.error('Streaming tests failed:', error);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    /**
     * Test AugmentContextEngine functionality
     */
    async testAugmentContextEngine(): Promise<void> {
        this.logger.info('Testing AugmentContextEngine...');

        const contextEngine = new AugmentContextEngine(this.logger, this.testDataDir);

        // Test context creation
        const context = contextEngine.createStreamContext('test-job', {
            batchSize: 1000,
            maxMemoryRecords: 5000,
            globalVariables: { testVar: 'testValue' }
        });

        // Test flow output updates
        const testRecords = this.generateTestRecords(100);
        await contextEngine.updateFlowOutput('test-job', 'test-flow', testRecords);

        // Test memory stats
        const memoryStats = contextEngine.getMemoryStats('test-job');
        if (memoryStats.totalRecords !== 100) {
            throw new Error(`Expected 100 records, got ${memoryStats.totalRecords}`);
        }

        // Test snapshots
        const snapshot = contextEngine.createSnapshot('test-job');
        if (snapshot.recordCounts['test-flow'] !== 100) {
            throw new Error(`Snapshot record count mismatch`);
        }

        // Test large dataset persistence
        const largeRecords = this.generateTestRecords(6000);
        await contextEngine.updateFlowOutput('test-job', 'large-flow', largeRecords);

        // Test retrieval
        const retrievedRecords = await contextEngine.getFlowOutput('test-job', 'large-flow');
        if (retrievedRecords.length !== 6000) {
            throw new Error(`Expected 6000 records, got ${retrievedRecords.length}`);
        }

        // Test cleanup
        await contextEngine.cleanup('test-job');

        this.logger.info('AugmentContextEngine tests passed!');
    }

    /**
     * Test StreamingCsvExtractor
     */
    async testStreamingCsvExtractor(): Promise<void> {
        this.logger.info('Testing StreamingCsvExtractor...');

        // Create test CSV file
        const testCsvPath = await this.createTestCsvFile(5000);

        const config = {
            type: 'csv' as const,
            filePath: testCsvPath,
            batchSize: 1000
        };

        const extractor = new StreamingCsvExtractor(config, this.logger);

        let totalRecords = 0;
        let batchCount = 0;

        for await (const batch of extractor.extract()) {
            totalRecords += batch.length;
            batchCount++;
            
            // Validate batch size
            if (batch.length > 1000) {
                throw new Error(`Batch size exceeded: ${batch.length}`);
            }

            // Validate record structure
            if (batch.length > 0) {
                const record = batch[0];
                if (!record.id || !record.name || !record.value) {
                    throw new Error('Invalid record structure');
                }
            }
        }

        if (totalRecords !== 5000) {
            throw new Error(`Expected 5000 records, got ${totalRecords}`);
        }

        this.logger.info(`StreamingCsvExtractor tests passed! Processed ${totalRecords} records in ${batchCount} batches`);
    }

    /**
     * Test StreamingCsvHelper
     */
    async testStreamingCsvHelper(): Promise<void> {
        this.logger.info('Testing StreamingCsvHelper...');

        const testRecords = this.generateTestRecords(1000);
        const outputPath = path.join(this.testDataDir, 'streaming-output.csv');

        // Test streaming write
        await StreamingCsvHelper.writeRecordsStreaming(testRecords, outputPath, 250);

        // Verify file was created and has correct content
        if (!fs.existsSync(outputPath)) {
            throw new Error('Output file was not created');
        }

        // Test append functionality
        const additionalRecords = this.generateTestRecords(500, 1000);
        await StreamingCsvHelper.appendToCsv(additionalRecords, outputPath);

        // Test reading back
        const readConfig = {
            type: 'csv' as const,
            filePath: outputPath,
            batchSize: 500
        };

        const readExtractor = new StreamingCsvExtractor(readConfig, this.logger);
        let readCount = 0;

        for await (const batch of readExtractor.extract()) {
            readCount += batch.length;
        }

        if (readCount !== 1500) {
            throw new Error(`Expected 1500 records, read ${readCount}`);
        }

        this.logger.info('StreamingCsvHelper tests passed!');
    }

    /**
     * Test Enhanced Orchestrator with streaming
     */
    async testEnhancedOrchestrator(): Promise<void> {
        this.logger.info('Testing Enhanced Orchestrator...');

        const orchestrator = createEnhancedEtlOrchestrator('debug', {
            enablePersistence: true,
            tempDirectory: this.testDataDir,
            maxMemoryRecords: 2000
        });

        // Test context engine access
        const contextEngine = orchestrator.getContextEngine();
        if (!contextEngine) {
            throw new Error('Context engine not accessible');
        }

        // Test memory stats
        const activeContexts = orchestrator.getActiveContexts();
        if (!Array.isArray(activeContexts)) {
            throw new Error('Active contexts should be an array');
        }

        this.logger.info('Enhanced Orchestrator tests passed!');
    }

    /**
     * Test memory management under load
     */
    async testMemoryManagement(): Promise<void> {
        this.logger.info('Testing memory management...');

        const contextEngine = new AugmentContextEngine(this.logger, this.testDataDir);
        const jobName = 'memory-test-job';

        // Create context with low memory threshold
        contextEngine.createStreamContext(jobName, {
            maxMemoryRecords: 1000,
            enablePersistence: true
        });

        // Add records in batches to test persistence
        for (let i = 0; i < 5; i++) {
            const records = this.generateTestRecords(500, i * 500);
            await contextEngine.updateFlowOutput(jobName, `flow-${i}`, records);
        }

        // Check memory stats
        const memoryStats = contextEngine.getMemoryStats(jobName);
        this.logger.info(`Memory usage: ${memoryStats.memoryUsageMB.toFixed(2)}MB`);

        // Test retrieval of persisted data
        const retrievedRecords = await contextEngine.getFlowOutput(jobName, 'flow-0');
        if (retrievedRecords.length === 0) {
            throw new Error('Failed to retrieve persisted records');
        }

        await contextEngine.cleanup(jobName);

        this.logger.info('Memory management tests passed!');
    }

    /**
     * Generate test records
     */
    private generateTestRecords(count: number, startId: number = 0): Record<string, any>[] {
        const records: Record<string, any>[] = [];
        
        for (let i = 0; i < count; i++) {
            records.push({
                id: startId + i,
                name: `Record ${startId + i}`,
                value: Math.random() * 1000,
                category: ['A', 'B', 'C'][i % 3],
                timestamp: new Date().toISOString(),
                active: i % 2 === 0
            });
        }

        return records;
    }

    /**
     * Create test CSV file
     */
    private async createTestCsvFile(recordCount: number): Promise<string> {
        const filePath = path.join(this.testDataDir, 'test-data.csv');
        const records = this.generateTestRecords(recordCount);

        await StreamingCsvHelper.writeRecordsStreaming(records, filePath, 1000);
        return filePath;
    }

    /**
     * Ensure test data directory exists
     */
    private ensureTestDataDir(): void {
        if (!fs.existsSync(this.testDataDir)) {
            fs.mkdirSync(this.testDataDir, { recursive: true });
        }
    }

    /**
     * Cleanup test files
     */
    private async cleanup(): Promise<void> {
        try {
            if (fs.existsSync(this.testDataDir)) {
                const files = fs.readdirSync(this.testDataDir);
                for (const file of files) {
                    const filePath = path.join(this.testDataDir, file);
                    if (fs.statSync(filePath).isFile()) {
                        fs.unlinkSync(filePath);
                    }
                }
                fs.rmdirSync(this.testDataDir);
            }
        } catch (error) {
            this.logger.warn('Cleanup failed:', error);
        }
    }
}

// Export test runner function
export async function runStreamingTests(): Promise<void> {
    const testSuite = new StreamingETLTest();
    await testSuite.runAllTests();
}
