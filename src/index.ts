import { EtlOrchestrator } from './core/orchestrator';
import { ConfigManager } from './config/manager';
import { ConnectionService } from './services/connectionService';
import { ExtractorFactory } from './extractors/extractorFactory';
import { TransformerFactory } from './transformers/transformerFactory';
import { LoaderFactory } from './loaders/loaderFactory';
import { Logger } from './utils/logger';

/**
 * Factory function to create ETL orchestrator with default configuration
 */
export function createEtlOrchestrator(logLevel?: string): EtlOrchestrator {
    const logger = new Logger(logLevel);
    const configManager = new ConfigManager(logger);
    const connectionService = new ConnectionService(logger);
    const extractorFactory = new ExtractorFactory(connectionService, logger);
    const transformerFactory = new TransformerFactory(logger);
    const loaderFactory = new LoaderFactory(connectionService, logger);

    return new EtlOrchestrator(
        configManager,
        connectionService,
        extractorFactory,
        transformerFactory,
        loaderFactory,
        logger
    );
}

/**
 * Run ETL job from configuration file
 */
export async function runEtlJob(
    configPath: string,
    options?: {
        logLevel?: string;
        recordLimit?: number;
    }
): Promise<void> {
    const orchestrator = createEtlOrchestrator(options?.logLevel);
    const result = await orchestrator.executeFromFile(configPath, options?.recordLimit);

    if (!result.success) {
        throw new Error(`ETL job failed: ${result.errors.map(e => e.message).join(', ')}`);
    }
}

/**
 * Validate ETL configuration file
 */
export function validateConfig(configPath: string, logLevel?: string): {
    valid: boolean;
    errors: string[];
} {
    const orchestrator = createEtlOrchestrator(logLevel);
    const errors = orchestrator.validateConfiguration(configPath);

    return {
        valid: errors.length === 0,
        errors
    };
}

// Export all main components
export * from './config';
export * from './core';
export * from './extractors';
export * from './transformers';
export * from './loaders';
export * from './services';
export * from './utils';
