import * as jsforce from "jsforce";
import { Loader, LoadResult } from "../core/interfaces";
import { TargetConfig } from "../config/schema";
import { ConnectionService } from "../services/connectionService";
import { Logger } from "../utils/logger";
import { CsvHelper } from "../utils/csvHelper";
import * as path from "path";

/**
 * Bulk API 2.0 loader for ETL v2
 * Simplified from original with consolidated CSV utilities
 */
export class BulkLoader implements Loader {
    private connection: jsforce.Connection | null = null;
    private accumulatedRecords: Record<string, any>[] = [];

    constructor(
        private config: TargetConfig,
        private connectionService: ConnectionService,
        private connectionName: string,
        private logger: Logger
    ) {}

    async load(records: Record<string, any>[]): Promise<LoadResult> {
        if (records.length === 0) {
            return {
                recordsProcessed: 0,
                recordsSucceeded: 0,
                recordsFailed: 0,
                errors: [],
                duration: 0,
                success: true,
            };
        }

        // Accumulate records for bulk processing
        this.accumulatedRecords.push(...records);
        this.logger.info(
            `Accumulated ${records.length} records. Total: ${this.accumulatedRecords.length}`
        );

        return {
            recordsProcessed: records.length,
            recordsSucceeded: records.length,
            recordsFailed: 0,
            errors: [],
            duration: 0,
            success: true,
        };
    }

    async finalize(): Promise<LoadResult> {
        if (this.accumulatedRecords.length === 0) {
            return {
                recordsProcessed: 0,
                recordsSucceeded: 0,
                recordsFailed: 0,
                errors: [],
                duration: 0,
                success: true,
            };
        }

        const startTime = Date.now();

        try {
            await this.initializeConnection();

            this.logger.info(
                `Starting Bulk API 2.0 load: ${this.accumulatedRecords.length} records to ${this.config.object}`
            );

            // Create job options
            const jobOptions: any = {
                object: this.config.object,
                operation: this.config.operation,
                lineEnding: "LF",
            };

            if (this.config.operation === "upsert" && this.config.externalId) {
                jobOptions.externalIdFieldName = this.config.externalId;
            }

            // Create and execute bulk job
            const job = this.connection!.bulk2.createJob(jobOptions);
            const jobInfo = await job.open();

            this.logger.info(`Bulk job opened: ${job.id}`);

            // Upload data
            await job.uploadData(this.accumulatedRecords as jsforce.Record[]);
            this.logger.info(`Data uploaded to bulk job ${job.id}`);

            // Close job and poll for completion
            await job.close();
            await job.poll(10000, 1200000); // 10s interval, 20min timeout

            // Get results
            const results = await job.getAllResults();
            const successfulResults = results.successfulResults || [];
            const failedResults = results.failedResults || [];
            const unprocessedRecords = Array.isArray(results.unprocessedRecords)
                ? results.unprocessedRecords
                : results.unprocessedRecords
                ? [results.unprocessedRecords]
                : [];

            // Save results to CSV
            await this.saveBulkResults(
                job.id,
                successfulResults,
                failedResults,
                unprocessedRecords
            );

            // Process results
            const allResults: any[] = [];
            const errors: any[] = [];

            // Add successful results
            successfulResults.forEach((result: any) => {
                allResults.push({
                    success: true,
                    id: result.sf__Id,
                    created: result.sf__Created === "true",
                });
            });

            // Add failed results
            failedResults.forEach((result: any) => {
                allResults.push({
                    success: false,
                    id: result.sf__Id,
                    error: result.sf__Error || "Unknown error",
                });
                errors.push({
                    message: `ID ${result.sf__Id || "N/A"}: ${
                        result.sf__Error || "Unknown error"
                    }`,
                });
            });

            // Add unprocessed records
            if (Array.isArray(unprocessedRecords)) {
                unprocessedRecords.forEach(() => {
                    allResults.push({
                        success: false,
                        error: "Record was not processed by Salesforce",
                    });
                    errors.push({
                        message: "Record was not processed by Salesforce",
                    });
                });
            }

            const duration = Date.now() - startTime;
            const finalJobInfo = job.getInfo();
            const finalSuccessCount =
                finalJobInfo.numberRecordsProcessed -
                finalJobInfo.numberRecordsFailed;
            const finalFailureCount = finalJobInfo.numberRecordsFailed || 0;

            this.logger.info(
                `Bulk operation completed: ${finalSuccessCount} successful, ${finalFailureCount} failed`
            );

            // Clear accumulated records
            const processedCount = this.accumulatedRecords.length;
            this.accumulatedRecords = [];

            return {
                recordsProcessed: processedCount,
                recordsSucceeded: finalSuccessCount,
                recordsFailed: finalFailureCount,
                errors,
                duration,
                results: allResults,
                success: finalFailureCount === 0 && errors.length === 0,
            };
        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error("Bulk operation failed", error);

            const processedCount = this.accumulatedRecords.length;
            this.accumulatedRecords = [];

            return {
                recordsProcessed: processedCount,
                recordsSucceeded: 0,
                recordsFailed: processedCount,
                errors: [
                    {
                        message:
                            error instanceof Error
                                ? error.message
                                : String(error),
                    },
                ],
                duration,
                success: false,
            };
        }
    }

    private async initializeConnection(): Promise<void> {
        if (!this.connection) {
            this.connection = await this.connectionService.getConnection(
                this.connectionName
            );
        }
    }

    private async saveBulkResults(
        jobId: string,
        successfulResults: any[],
        failedResults: any[],
        unprocessedRecords: any[]
    ): Promise<void> {
        try {
            const resultsDir = path.join(
                process.cwd(),
                "staging",
                "job_results",
                jobId
            );

            // Save successful results
            if (successfulResults.length > 0) {
                const successFilePath = path.join(
                    resultsDir,
                    "successful_records.csv"
                );
                await CsvHelper.writeToCsv(successfulResults, successFilePath);
                this.logger.info(
                    `Saved ${successfulResults.length} successful records`
                );
            }

            // Save failed results
            if (failedResults.length > 0) {
                const failureFilePath = path.join(
                    resultsDir,
                    "failed_records.csv"
                );
                await CsvHelper.writeToCsv(failedResults, failureFilePath);
                this.logger.info(
                    `Saved ${failedResults.length} failed records`
                );
            }

            // Save unprocessed records
            if (
                Array.isArray(unprocessedRecords) &&
                unprocessedRecords.length > 0
            ) {
                const unprocessedFilePath = path.join(
                    resultsDir,
                    "unprocessed_records.csv"
                );
                await CsvHelper.writeToCsv(
                    unprocessedRecords,
                    unprocessedFilePath
                );
                this.logger.info(
                    `Saved ${unprocessedRecords.length} unprocessed records`
                );
            }
        } catch (error) {
            this.logger.error(
                `Failed to save bulk results for job ${jobId}`,
                error
            );
        }
    }
}
