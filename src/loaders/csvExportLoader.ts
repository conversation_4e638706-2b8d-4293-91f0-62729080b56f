import { Loader, LoadResult } from '../core/interfaces';
import { TargetConfig } from '../config/schema';
import { Logger } from '../utils/logger';
import { CsvHelper } from '../utils/csvHelper';

/**
 * CSV export loader for ETL v2
 * Simplified using consolidated CSV utilities
 */
export class CsvExportLoader implements Loader {
    constructor(
        private config: TargetConfig,
        private logger: Logger
    ) {
        if (!config.filePath) {
            throw new Error('CSV export loader requires filePath in configuration');
        }
    }

    async load(records: Record<string, any>[]): Promise<LoadResult> {
        const startTime = Date.now();

        try {
            this.logger.info(`Starting CSV export: ${records.length} records to ${this.config.filePath}`);

            if (records.length === 0) {
                this.logger.warn('No records to export');
                return {
                    recordsProcessed: 0,
                    recordsSucceeded: 0,
                    recordsFailed: 0,
                    errors: [],
                    duration: Date.now() - startTime,
                    success: true
                };
            }

            // Write records to CSV using consolidated utility
            await CsvHelper.writeToCsv(records, this.config.filePath!, false);

            const duration = Date.now() - startTime;
            this.logger.info(`CSV export completed: ${records.length} records exported in ${duration}ms`);

            return {
                recordsProcessed: records.length,
                recordsSucceeded: records.length,
                recordsFailed: 0,
                errors: [],
                duration,
                success: true
            };

        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('CSV export failed', error);

            return {
                recordsProcessed: records.length,
                recordsSucceeded: 0,
                recordsFailed: records.length,
                errors: [{ message: error instanceof Error ? error.message : String(error) }],
                duration,
                success: false
            };
        }
    }
}
