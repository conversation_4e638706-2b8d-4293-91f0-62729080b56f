import { Loader } from '../core/interfaces';
import { TargetConfig, ConnectionConfig } from '../config/schema';
import { ConnectionService } from '../services/connectionService';
import { Logger } from '../utils/logger';
import { RestLoader } from './restLoader';
import { BulkLoader } from './bulkLoader';
import { CsvExportLoader } from './csvExportLoader';

/**
 * Simplified loader factory for ETL v2
 */
export class LoaderFactory {
    constructor(
        private connectionService: ConnectionService,
        private logger: Logger
    ) {}

    /**
     * Create a loader based on target configuration
     */
    async createLoader(
        config: TargetConfig,
        connections: Record<string, ConnectionConfig>
    ): Promise<Loader> {
        this.logger.debug(`Creating loader for strategy: ${config.loadStrategy}`);

        // Initialize connections first
        await this.connectionService.initializeConnections(connections);

        switch (config.loadStrategy) {
            case 'rest':
                return this.createRestLoader(config, connections);

            case 'bulk2':
                return this.createBulkLoader(config, connections);

            case 'csv-export':
                return new CsvExportLoader(config, this.logger);

            default:
                throw new Error(`Unsupported load strategy: ${config.loadStrategy}`);
        }
    }

    /**
     * Create multiple loaders for multi-target flows
     */
    async createMultipleLoaders(
        targets: TargetConfig[],
        connections: Record<string, ConnectionConfig>
    ): Promise<Map<string, Loader>> {
        const loaders = new Map<string, Loader>();

        // Initialize connections once for all loaders
        await this.connectionService.initializeConnections(connections);

        for (const target of targets) {
            const loader = this.createLoaderDirect(target, connections);
            loaders.set(target.name, loader);
        }

        return loaders;
    }

    /**
     * Create loader without initializing connections (for internal use)
     */
    private createLoaderDirect(
        config: TargetConfig,
        connections: Record<string, ConnectionConfig>
    ): Loader {
        switch (config.loadStrategy) {
            case 'rest':
                return this.createRestLoader(config, connections);

            case 'bulk2':
                return this.createBulkLoader(config, connections);

            case 'csv-export':
                return new CsvExportLoader(config, this.logger);

            default:
                throw new Error(`Unsupported load strategy: ${config.loadStrategy}`);
        }
    }

    /**
     * Create REST API loader
     */
    private createRestLoader(
        config: TargetConfig,
        connections: Record<string, ConnectionConfig>
    ): RestLoader {
        const connectionConfig = connections[config.connection];
        if (!connectionConfig) {
            throw new Error(`Connection configuration not found: ${config.connection}`);
        }

        return new RestLoader(
            config,
            this.connectionService,
            config.connection,
            this.logger
        );
    }

    /**
     * Create Bulk API 2.0 loader
     */
    private createBulkLoader(
        config: TargetConfig,
        connections: Record<string, ConnectionConfig>
    ): BulkLoader {
        const connectionConfig = connections[config.connection];
        if (!connectionConfig) {
            throw new Error(`Connection configuration not found: ${config.connection}`);
        }

        return new BulkLoader(
            config,
            this.connectionService,
            config.connection,
            this.logger
        );
    }
}
