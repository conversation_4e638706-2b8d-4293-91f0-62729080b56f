import * as jsforce from 'jsforce';
import { Loader, LoadResult } from '../core/interfaces';
import { TargetConfig } from '../config/schema';
import { ConnectionService } from '../services/connectionService';
import { Logger } from '../utils/logger';

/**
 * REST API loader for ETL v2
 * Simplified from original
 */
export class RestLoader implements Loader {
    private connection: jsforce.Connection | null = null;

    constructor(
        private config: TargetConfig,
        private connectionService: ConnectionService,
        private connectionName: string,
        private logger: Logger
    ) {}

    async load(records: Record<string, any>[]): Promise<LoadResult> {
        const startTime = Date.now();

        try {
            await this.initializeConnection();

            const batchSize = this.config.batchSize || 200;
            const results: any[] = [];
            const errors: any[] = [];
            let successCount = 0;

            this.logger.info(`Starting REST API load: ${records.length} records to ${this.config.object}`);

            // Process in batches
            for (let i = 0; i < records.length; i += batchSize) {
                const batch = records.slice(i, i + batchSize);
                const batchResult = await this.processBatch(batch);

                results.push(...batchResult.results);
                errors.push(...batchResult.errors);
                successCount += batchResult.successCount;

                this.logger.debug(`REST batch processed: ${batchResult.successCount}/${batch.length} successful`);
            }

            const duration = Date.now() - startTime;
            this.logger.info(`REST API load completed: ${successCount}/${records.length} successful in ${duration}ms`);

            return {
                recordsProcessed: records.length,
                recordsSucceeded: successCount,
                recordsFailed: records.length - successCount,
                errors,
                duration,
                results,
                success: errors.length === 0
            };

        } catch (error) {
            const duration = Date.now() - startTime;
            this.logger.error('REST API load failed', error);

            return {
                recordsProcessed: records.length,
                recordsSucceeded: 0,
                recordsFailed: records.length,
                errors: [{ message: error instanceof Error ? error.message : String(error) }],
                duration,
                success: false
            };
        }
    }

    private async initializeConnection(): Promise<void> {
        if (!this.connection) {
            this.connection = await this.connectionService.getConnection(this.connectionName);
        }
    }

    private async processBatch(records: Record<string, any>[]): Promise<{
        results: any[];
        errors: any[];
        successCount: number;
    }> {
        const results: any[] = [];
        const errors: any[] = [];
        let successCount = 0;

        try {
            let batchResults: any[];

            switch (this.config.operation) {
                case 'insert':
                    batchResults = await this.connection!.sobject(this.config.object).create(records);
                    break;

                case 'update':
                    const updateRecords = records.filter(r => r.Id) as Array<Record<string, any> & { Id: string }>;
                    if (updateRecords.length !== records.length) {
                        throw new Error('Update operation requires all records to have an Id field');
                    }
                    batchResults = await this.connection!.sobject(this.config.object).update(updateRecords);
                    break;

                case 'upsert':
                    if (!this.config.externalId) {
                        throw new Error('External ID field is required for upsert operation');
                    }
                    batchResults = await this.connection!.sobject(this.config.object).upsert(records, this.config.externalId);
                    break;

                case 'delete':
                    const ids = records.map(r => r.Id).filter(id => id);
                    batchResults = await this.connection!.sobject(this.config.object).destroy(ids);
                    break;

                default:
                    throw new Error(`Unsupported operation: ${this.config.operation}`);
            }

            // Process results
            const resultsArray = Array.isArray(batchResults) ? batchResults : [batchResults];

            resultsArray.forEach((result, index) => {
                if (result.success) {
                    successCount++;
                    results.push({
                        success: true,
                        id: result.id,
                        created: result.created || false
                    });
                } else {
                    results.push({
                        success: false,
                        id: result.id || null,
                        errors: result.errors || [{ message: 'Unknown error' }]
                    });
                    errors.push({
                        recordIndex: index,
                        error: result.errors && result.errors.length > 0
                            ? `${result.errors[0].statusCode}: ${result.errors[0].message}`
                            : 'Unknown error'
                    });
                }
            });

        } catch (error) {
            this.logger.error('REST API batch processing failed', error);
            errors.push({ message: error instanceof Error ? error.message : String(error) });
        }

        return { results, errors, successCount };
    }
}
