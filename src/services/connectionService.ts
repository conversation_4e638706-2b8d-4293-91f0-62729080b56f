import * as jsforce from 'jsforce';
import { exec } from 'child_process';
import { promisify } from 'util';
import { ConnectionConfig } from '../config/schema';
import { Logger } from '../utils/logger';

const execAsync = promisify(exec);

/**
 * Simplified Connection Service for ETL v2
 * Removed some complexity while keeping core functionality
 */
export class ConnectionService {
    private connections = new Map<string, jsforce.Connection>();

    constructor(private logger: Logger) {}

    /**
     * Initialize multiple connections at once
     */
    async initializeConnections(connections: Record<string, ConnectionConfig>): Promise<void> {
        let successCount = 0;

        for (const [name, config] of Object.entries(connections)) {
            try {
                await this.getConnection(name, config);
                successCount++;
            } catch (error) {
                this.logger.error(`Failed to initialize connection: ${name}`, { error });
            }
        }

        this.logger.info(`Initialized ${successCount} connections`);
    }

    /**
     * Get or create a connection
     */
    async getConnection(connectionName: string, config?: ConnectionConfig): Promise<jsforce.Connection> {
        // Return cached connection if exists and valid
        if (this.connections.has(connectionName)) {
            const conn = this.connections.get(connectionName)!;
            if (this.isConnectionValid(conn)) {
                return conn;
            } else {
                this.connections.delete(connectionName);
            }
        }

        // If no config provided, throw error for missing connection
        if (!config) {
            throw new Error(`Connection not found: ${connectionName}`);
        }

        // Create new connection
        const connection = await this.createConnection(config);
        this.connections.set(connectionName, connection);

        this.logger.debug(`Created connection: ${connectionName}`);
        return connection;
    }

    /**
     * Test if a connection is working
     */
    async testConnection(config: ConnectionConfig): Promise<boolean> {
        try {
            const connection = await this.createConnection(config);
            await connection.limits();
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * Close all connections
     */
    closeAllConnections(): void {
        this.connections.clear();
        this.logger.info('Closed all connections');
    }

    /**
     * Create a new connection based on configuration
     */
    private async createConnection(config: ConnectionConfig): Promise<jsforce.Connection> {
        let connection: jsforce.Connection;

        if (config.sfdxAlias) {
            connection = await this.createSfdxConnection(config.sfdxAlias, config.apiVersion);
        } else if (config.accessToken && config.instanceUrl) {
            // OAuth token connection
            connection = new jsforce.Connection({
                accessToken: config.accessToken,
                instanceUrl: config.instanceUrl,
                version: config.apiVersion
            });
        } else if (config.username && config.password) {
            connection = await this.createDirectConnection(config);
        } else {
            throw new Error('Connection configuration must include either sfdxAlias, accessToken/instanceUrl, or username/password');
        }

        return connection;
    }

    /**
     * Create connection using SFDX
     */
    private async createSfdxConnection(alias: string, apiVersion?: string): Promise<jsforce.Connection> {
        try {
            const orgInfo = await this.getSfdxOrgInfo(alias);

            const connection = new jsforce.Connection({
                instanceUrl: orgInfo.instanceUrl,
                accessToken: orgInfo.accessToken,
                version: apiVersion || '57.0'
            });

            await connection.identity();

            this.logger.debug(`SFDX connection established for alias: ${alias}`);
            return connection;

        } catch (error) {
            throw new Error(`Failed to create SFDX connection for alias "${alias}": ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Create direct connection using username/password
     */
    private async createDirectConnection(config: ConnectionConfig): Promise<jsforce.Connection> {
        if (!config.username || !config.password) {
            throw new Error('Username and password are required for direct connection');
        }

        try {
            const connectionOptions: any = {
                loginUrl: config.instanceUrl || 'https://login.salesforce.com'
            };

            if (config.apiVersion) {
                connectionOptions.version = config.apiVersion;
            }

            const connection = new jsforce.Connection(connectionOptions);

            const password = config.securityToken
                ? config.password + config.securityToken
                : config.password;

            await connection.login(config.username!, password!);

            this.logger.debug(`Direct connection established for user: ${config.username}`);
            return connection;

        } catch (error) {
            throw new Error(`Failed to create direct connection for user "${config.username}": ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * Get SFDX org information
     */
    private async getSfdxOrgInfo(alias: string): Promise<{ instanceUrl: string; accessToken: string }> {
        return new Promise((resolve, reject) => {
            const command = `sfdx force:org:display --targetusername ${alias} --json`;

            exec(command, (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`SFDX command failed: ${error.message}`));
                    return;
                }

                try {
                    const result = JSON.parse(stdout);
                    if (result.status === 0 && result.result) {
                        resolve({
                            instanceUrl: result.result.instanceUrl,
                            accessToken: result.result.accessToken
                        });
                    } else {
                        reject(new Error(`SFDX org info not found for alias: ${alias}`));
                    }
                } catch (error) {
                    reject(new Error(`Failed to parse SFDX output: ${error instanceof Error ? error.message : String(error)}`));
                }
            });
        });
    }

    /**
     * Check if connection is still valid
     */
    private isConnectionValid(connection: jsforce.Connection): boolean {
        try {
            return !!(connection.accessToken && connection.instanceUrl);
        } catch {
            return false;
        }
    }
}
