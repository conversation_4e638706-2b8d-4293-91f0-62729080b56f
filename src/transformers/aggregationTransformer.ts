import { Transformer, JobContext } from '../core/interfaces';
import { AggregationTransformConfig } from '../config/schema';
import { Logger } from '../utils/logger';
import { ExpressionEvaluator } from '../utils/expressionEvaluator';

/**
 * Aggregation transformer - performs SQL-like GROUP BY operations
 * Simplified from original with consolidated utilities
 */
export class AggregationTransformer implements Transformer {
    constructor(
        private config: AggregationTransformConfig,
        private context?: JobContext,
        private logger?: Logger
    ) {}

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        this.logger?.info(`Starting aggregation transformation with ${records.length} source records`);

        // Apply pre-transform filtering if specified
        let filteredRecords = records;
        if (this.config.filter) {
            filteredRecords = this.applyFilter(records, this.config.filter);
            this.logger?.info(`Filter applied: ${filteredRecords.length} records remaining`);
        }

        // Group records by the specified fields
        const groups = new Map<string, Record<string, any>[]>();

        for (const record of filteredRecords) {
            const groupKey = this.buildGroupKey(record, this.config.groupBy);
            if (!groups.has(groupKey)) {
                groups.set(groupKey, []);
            }
            groups.get(groupKey)!.push(record);
        }

        // Apply aggregations to each group
        const aggregatedRecords: Record<string, any>[] = [];

        for (const [groupKey, groupRecords] of groups) {
            const aggregatedRecord: Record<string, any> = {};

            // Add group by fields to the result
            const keyParts = groupKey.split('::');
            this.config.groupBy.forEach((field, index) => {
                aggregatedRecord[field] = keyParts[index] === 'null' ? null : keyParts[index];
            });

            // Apply each aggregate function
            for (const aggregate of this.config.aggregates) {
                const value = this.applyAggregate(groupRecords, aggregate);
                aggregatedRecord[aggregate.targetField] = value;
            }

            aggregatedRecords.push(aggregatedRecord);
        }

        // Apply field mappings if specified
        let mappedRecords = aggregatedRecords;
        if (this.config.mappings && Object.keys(this.config.mappings).length > 0) {
            mappedRecords = this.applyMappings(aggregatedRecords, this.config.mappings);
            this.logger?.debug('Field mappings applied to aggregated records');
        }

        this.logger?.info(`Aggregation transformation completed: ${mappedRecords.length} output records`);
        return mappedRecords;
    }

    private buildGroupKey(record: Record<string, any>, fields: string[]): string {
        return fields
            .map(field => String(record[field] ?? 'null'))
            .join('::');
    }

    private applyAggregate(
        records: Record<string, any>[],
        aggregate: any
    ): any {
        const values = records
            .map(r => r[aggregate.field])
            .filter(v => v !== null && v !== undefined);

        switch (aggregate.operation) {
            case 'sum':
                return values.reduce((sum, val) => sum + Number(val), 0);

            case 'count':
                if (aggregate.options?.distinct) {
                    return new Set(values).size;
                }
                return values.length;

            case 'avg':
                if (values.length === 0) return null;
                const sum = values.reduce((s, val) => s + Number(val), 0);
                return sum / values.length;

            case 'min':
                if (values.length === 0) return null;
                return Math.min(...values.map(Number));

            case 'max':
                if (values.length === 0) return null;
                return Math.max(...values.map(Number));

            case 'concat':
                const separator = aggregate.options?.separator || ', ';
                return values.join(separator);

            default:
                throw new Error(`Unknown aggregate operation: ${aggregate.operation}`);
        }
    }

    /**
     * Apply filtering to records
     */
    private applyFilter(records: Record<string, any>[], filterExpression: string): Record<string, any>[] {
        return records.filter(record => {
            try {
                return ExpressionEvaluator.evaluateFilter(
                    filterExpression, 
                    record,
                    {
                        globalVariables: this.context?.globalVariables,
                        flowOutputs: this.context?.flowOutputs
                    }
                );
            } catch (error) {
                this.logger?.warn(`Filter evaluation failed for record: ${error instanceof Error ? error.message : String(error)}`);
                return false;
            }
        });
    }

    /**
     * Apply field mappings to aggregated records
     */
    private applyMappings(records: Record<string, any>[], mappings: Record<string, string>): Record<string, any>[] {
        return records.map(record => {
            const mappedRecord: Record<string, any> = {};

            // Check for wildcard mapping
            const hasWildcard = mappings['*'] === '*';
            if (hasWildcard) {
                Object.assign(mappedRecord, record);
            }

            // Apply explicit mappings
            for (const [targetField, sourceExpression] of Object.entries(mappings)) {
                if (targetField === '*' && sourceExpression === '*') {
                    continue;
                }

                try {
                    const value = ExpressionEvaluator.evaluate(
                        sourceExpression, 
                        record,
                        {
                            globalVariables: this.context?.globalVariables,
                            flowOutputs: this.context?.flowOutputs
                        }
                    );
                    mappedRecord[targetField] = value;
                } catch (error) {
                    this.logger?.warn(`Mapping evaluation failed for field ${targetField}: ${error instanceof Error ? error.message : String(error)}`);
                }
            }

            return mappedRecord;
        });
    }
}
