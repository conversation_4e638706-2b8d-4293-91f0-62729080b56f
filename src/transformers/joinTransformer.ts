import { Transformer, JobContext } from "../core/interfaces";
import { JoinTransformConfig } from "../config/schema";
import { Logger } from "../utils/logger";
import { ExpressionEvaluator } from "../utils/expressionEvaluator";
import { CsvHelper } from "../utils/csvHelper";
import * as path from 'path';

/**
 * Join transformer for joining multiple data sources
 * Simplified from the original many-to-one transformer
 */
export class JoinTransformer implements Transformer {
    private sourceData = new Map<string, Record<string, any>[]>();
    private joinIndexes = new Map<string, Map<string, Record<string, any>[]>>();

    constructor(
        private config: JoinTransformConfig,
        private context: JobContext | undefined,
        private logger: Logger,
        private debugOptions?: { saveTransformerCsv?: boolean }
    ) {}

    /**
     * Set source data for joining and build hash indexes
     */
    setSourceData(alias: string, data: Record<string, any>[]): void {
        this.sourceData.set(alias, data);
        this.logger.debug(`Set source data for alias ${alias}: ${data.length} records`);

        // Build indexes for this source
        const relevantJoins = this.config.joins.filter(j => j.sourceAlias === alias);

        for (const join of relevantJoins) {
            const indexKey = `${alias}_join`;
            if (!this.joinIndexes.has(indexKey)) {
                this.joinIndexes.set(indexKey, new Map());
            }

            const index = this.joinIndexes.get(indexKey)!;

            // Build composite key index for efficient lookups
            for (const record of data) {
                const compositeKey = ExpressionEvaluator.buildCompositeKey(
                    record,
                    join.on.map(c => c.joinField)
                );

                if (!index.has(compositeKey)) {
                    index.set(compositeKey, []);
                }
                index.get(compositeKey)!.push(record);
            }

            this.logger.debug(`Built hash index for ${alias} with ${index.size} unique keys`);
        }
    }

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        // Load source data from context if not already set
        if (this.context && this.sourceData.size === 0) {
            // Load primary source data
            const primaryData = this.context.flowOutputs.get(this.config.primarySource);
            if (primaryData) {
                this.setSourceData(this.config.primarySource, primaryData);
            }
            
            // Load join source data
            for (const join of this.config.joins) {
                const joinData = this.context.flowOutputs.get(join.sourceAlias);
                if (joinData) {
                    this.setSourceData(join.sourceAlias, joinData);
                } else {
                    this.logger.warn(`No data found in context for join source: ${join.sourceAlias}`);
                }
            }
        }

        const primarySourceAlias = this.config.primarySource;
        const transformedRecords: Record<string, any>[] = [];

        this.logger.info(`Starting join transformation with ${records.length} primary records`);

        // Process each primary record
        for (const primaryRecord of records) {
            // Start with prefixed primary record fields
            const joinedRecord: Record<string, any> = {};
            for (const [key, value] of Object.entries(primaryRecord)) {
                joinedRecord[`${primarySourceAlias}_${key}`] = value;
            }

            let shouldSkipRecord = false;

            // Perform joins using hash lookups
            for (const join of this.config.joins) {
                const joinData = this.performHashJoin(primaryRecord, join);

                if (joinData) {
                    // Add prefixed join data to result
                    for (const [key, value] of Object.entries(joinData)) {
                        joinedRecord[`${join.sourceAlias}_${key}`] = value;
                    }
                } else if (join.joinType === "inner") {
                    // Skip record if inner join fails
                    shouldSkipRecord = true;
                    break;
                }
            }

            if (shouldSkipRecord) {
                this.logger.debug(`Skipping record due to failed inner join`);
                continue;
            }

            // Apply mappings to the complete joined record
            const mappedRecord = await this.applyMappings(joinedRecord);

            // Apply filter if specified
            if (this.config.filter) {
                try {
                    const shouldInclude = ExpressionEvaluator.evaluateFilter(
                        this.config.filter, 
                        mappedRecord,
                        {
                            globalVariables: this.context?.globalVariables,
                            flowOutputs: this.context?.flowOutputs
                        }
                    );
                    if (!shouldInclude) {
                        this.logger.debug(`Record filtered out by expression: ${this.config.filter}`);
                        continue;
                    }
                } catch (error) {
                    this.logger.warn(`Filter expression evaluation failed, including record by default: ${error instanceof Error ? error.message : String(error)}`);
                }
            }

            transformedRecords.push(mappedRecord);
        }

        this.logger.info(`Join transformation completed: ${transformedRecords.length} records`);

        // Save debug CSV if enabled
        await this.saveBatchIfDebug(transformedRecords);

        return transformedRecords;
    }

    /**
     * Perform hash-based join for O(1) lookup performance
     */
    private performHashJoin(
        primaryRecord: Record<string, any>,
        joinConfig: {
            sourceAlias: string;
            joinType: string;
            on: Array<{ primaryField: string; joinField: string }>;
        }
    ): Record<string, any> | null {
        const indexKey = `${joinConfig.sourceAlias}_join`;
        const index = this.joinIndexes.get(indexKey);

        if (!index) {
            this.logger.warn(`No index found for ${indexKey}`);
            return null;
        }

        // Build lookup key from primary record values
        const primaryFields = joinConfig.on.map(c => c.primaryField);
        const lookupKey = ExpressionEvaluator.buildCompositeKey(primaryRecord, primaryFields);

        // Perform O(1) hash lookup
        const matches = index.get(lookupKey);

        if (matches && matches.length > 0) {
            if (matches.length > 1) {
                this.logger.debug(`Found ${matches.length} matches for join, using first match`);
            }
            return matches[0];
        }

        return null;
    }

    /**
     * Apply field mappings to the joined record
     */
    private async applyMappings(record: Record<string, any>): Promise<Record<string, any>> {
        const mappings = this.config.mappings;
        const mappedRecord: Record<string, any> = {};

        // Check if wildcard mapping exists
        const hasWildcard = mappings['*'] === '*';
        if (hasWildcard) {
            Object.assign(mappedRecord, record);
        }

        // Apply explicit mappings
        for (const [targetField, sourceExpression] of Object.entries(mappings)) {
            if (targetField === '*' && sourceExpression === '*') {
                continue;
            }

            try {
                const value = ExpressionEvaluator.evaluate(
                    sourceExpression, 
                    record,
                    {
                        globalVariables: this.context?.globalVariables,
                        flowOutputs: this.context?.flowOutputs
                    }
                );
                mappedRecord[targetField] = value;
            } catch (error) {
                this.logger.warn(`Failed to evaluate expression for field ${targetField}: ${error instanceof Error ? error.message : String(error)}`);
                mappedRecord[targetField] = null;
            }
        }

        return mappedRecord;
    }

    private async saveBatchIfDebug(batch: Record<string, any>[]): Promise<void> {
        if (!this.debugOptions?.saveTransformerCsv || !batch.length) return;

        try {
            const jobName = this.context?.jobName || 'unknownJob';
            const filePath = path.join(process.cwd(), 'staging', `${jobName}_join_transformed.csv`);
            await CsvHelper.writeToCsv(batch, filePath, true);
        } catch (error) {
            this.logger?.warn('Failed to save debug CSV', error);
        }
    }
}
