import { Transformer, JobContext } from '../core/interfaces';
import { LookupTransformConfig } from '../config/schema';
import { Logger } from '../utils/logger';
import { ExpressionEvaluator } from '../utils/expressionEvaluator';
import { CsvHelper } from '../utils/csvHelper';
import * as path from 'path';

/**
 * Lookup transformer for finding master data records
 * Simplified from original with consolidated utilities
 */
export class LookupTransformer implements Transformer {
    private lookupData: Record<string, any>[] = [];

    constructor(
        private config: LookupTransformConfig,
        private context: JobContext | undefined,
        private logger: Logger,
        private debugOptions?: { saveTransformerCsv?: boolean }
    ) {}

    /**
     * Set lookup data for matching
     */
    setLookupData(data: Record<string, any>[]): void {
        this.lookupData = data;
        this.logger.debug(`Set lookup data: ${data.length} records`);
    }

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        // Load lookup data from context if needed
        if (this.config.sourceAlias && this.context) {
            const contextData = this.context.flowOutputs.get(this.config.sourceAlias);
            if (contextData) {
                this.setLookupData(contextData);
            } else {
                this.logger.warn(`No lookup data found in context for alias: ${this.config.sourceAlias}`);
            }
        }

        if (this.lookupData.length === 0) {
            this.logger.warn('No lookup data available for transformation');
        }

        const transformedRecords: Record<string, any>[] = [];

        for (const record of records) {
            const transformedRecord = await this.processRecord(record);
            if (transformedRecord !== null) {
                transformedRecords.push(transformedRecord);
            }
        }

        // Save debug CSV if enabled
        await this.saveBatchIfDebug(transformedRecords);

        return transformedRecords;
    }

    /**
     * Process a single record with lookup
     */
    private async processRecord(record: Record<string, any>): Promise<Record<string, any> | null> {
        const matches = this.findMatches(record);
        const lookupConfig = this.config;

        // Handle no matches
        if (matches.length === 0) {
            switch (lookupConfig.noMatchStrategy || 'null') {
                case 'error':
                    throw new Error(`No lookup match found for record: ${JSON.stringify(record)}`);
                case 'skip':
                    this.logger.debug('Skipping record due to no lookup match');
                    return null;
                case 'null':
                default:
                    break;
            }
        }

        // Handle multiple matches
        let selectedMatch: Record<string, any> | null = null;
        if (matches.length > 0) {
            switch (lookupConfig.multipleMatchStrategy || 'first') {
                case 'error':
                    if (matches.length > 1) {
                        throw new Error(`Multiple lookup matches (${matches.length}) found for record`);
                    }
                    selectedMatch = matches[0];
                    break;
                case 'last':
                    selectedMatch = matches[matches.length - 1];
                    break;
                case 'first':
                default:
                    selectedMatch = matches[0];
                    break;
            }
        }

        // Apply mappings
        const transformedRecord = { ...record };

        // Add the lookup result ID
        if (selectedMatch) {
            transformedRecord[lookupConfig.resultField] = selectedMatch.Id || selectedMatch.id;

            // Apply additional field mappings if specified
            if (lookupConfig.resultMappings) {
                for (const [targetField, lookupField] of Object.entries(lookupConfig.resultMappings)) {
                    transformedRecord[targetField] = selectedMatch[lookupField];
                }
            }
        } else {
            transformedRecord[lookupConfig.resultField] = null;
        }

        // Apply standard field mappings
        if (this.config.mappings) {
            for (const [targetField, sourceExpression] of Object.entries(this.config.mappings)) {
                if (targetField === '*' && sourceExpression === '*') {
                    continue;
                }
                try {
                    const value = ExpressionEvaluator.evaluate(
                        sourceExpression, 
                        transformedRecord,
                        {
                            globalVariables: this.context?.globalVariables,
                            flowOutputs: this.context?.flowOutputs
                        }
                    );
                    transformedRecord[targetField] = value;
                } catch (error) {
                    this.logger.warn(`Failed to evaluate expression for field ${targetField}: ${error instanceof Error ? error.message : String(error)}`);
                    transformedRecord[targetField] = null;
                }
            }
        }

        // Apply filter if specified
        if (this.config.filter) {
            try {
                const shouldInclude = ExpressionEvaluator.evaluateFilter(
                    this.config.filter, 
                    transformedRecord,
                    {
                        globalVariables: this.context?.globalVariables,
                        flowOutputs: this.context?.flowOutputs
                    }
                );
                if (!shouldInclude) {
                    this.logger.debug(`Record filtered out by expression: ${this.config.filter}`);
                    return null;
                }
            } catch (error) {
                this.logger.warn(`Filter expression evaluation failed, including record by default: ${error instanceof Error ? error.message : String(error)}`);
            }
        }

        return transformedRecord;
    }

    /**
     * Find all matching lookup records for a given record
     */
    private findMatches(record: Record<string, any>): Record<string, any>[] {
        const conditions = this.config.conditions;

        return this.lookupData.filter(lookupRecord => {
            return conditions.every(condition => {
                return this.evaluateCondition(record, lookupRecord, condition);
            });
        });
    }

    /**
     * Evaluate a single condition
     */
    private evaluateCondition(
        record: Record<string, any>,
        lookupRecord: Record<string, any>,
        condition: any
    ): boolean {
        const sourceValue = record[condition.sourceField];

        switch (condition.type) {
            case 'dateRange':
                return this.evaluateDateRangeCondition(sourceValue, lookupRecord, condition);
            case 'equals':
                const lookupValue = condition.value !== undefined ? condition.value : lookupRecord[condition.lookupField];
                return ExpressionEvaluator.normalizeValue(sourceValue) === ExpressionEvaluator.normalizeValue(lookupValue);
            case 'greaterThan':
                return this.evaluateNumericCondition(sourceValue, lookupRecord, condition, '>');
            case 'lessThan':
                return this.evaluateNumericCondition(sourceValue, lookupRecord, condition, '<');
            case 'greaterThanOrEquals':
                return this.evaluateNumericCondition(sourceValue, lookupRecord, condition, '>=');
            case 'lessThanOrEquals':
                return this.evaluateNumericCondition(sourceValue, lookupRecord, condition, '<=');
            case 'boolean':
                return this.evaluateBooleanCondition(sourceValue, lookupRecord, condition);
            default:
                this.logger.warn(`Unknown condition type: ${condition.type}`);
                return false;
        }
    }

    /**
     * Evaluate date range condition
     */
    private evaluateDateRangeCondition(
        sourceValue: any,
        lookupRecord: Record<string, any>,
        condition: any
    ): boolean {
        if (!sourceValue || !condition.lookupStartField || !condition.lookupEndField) {
            return false;
        }

        const sourceDate = ExpressionEvaluator.parseDate(sourceValue);
        const startDate = ExpressionEvaluator.parseDate(lookupRecord[condition.lookupStartField]);
        const endDate = ExpressionEvaluator.parseDate(lookupRecord[condition.lookupEndField]);

        if (!sourceDate || !startDate) {
            return false;
        }

        if (sourceDate < startDate) {
            return false;
        }

        // If endDate is null/undefined, treat as ongoing
        if (!endDate) {
            return true;
        }

        return sourceDate <= endDate;
    }

    /**
     * Evaluate numeric condition
     */
    private evaluateNumericCondition(
        sourceValue: any,
        lookupRecord: Record<string, any>,
        condition: any,
        operator: '>' | '<' | '>=' | '<='
    ): boolean {
        const sourceNum = Number(sourceValue);
        const compareValue = condition.value !== undefined ?
            Number(condition.value) :
            Number(lookupRecord[condition.lookupField]);

        if (isNaN(sourceNum) || isNaN(compareValue)) {
            return false;
        }

        switch (operator) {
            case '>': return sourceNum > compareValue;
            case '<': return sourceNum < compareValue;
            case '>=': return sourceNum >= compareValue;
            case '<=': return sourceNum <= compareValue;
            default: return false;
        }
    }

    /**
     * Evaluate boolean condition
     */
    private evaluateBooleanCondition(
        sourceValue: any,
        lookupRecord: Record<string, any>,
        condition: any
    ): boolean {
        const sourceBool = ExpressionEvaluator.toBoolean(sourceValue);
        const compareBool = condition.value !== undefined ?
            ExpressionEvaluator.toBoolean(condition.value) :
            ExpressionEvaluator.toBoolean(lookupRecord[condition.lookupField]);

        return sourceBool === compareBool;
    }

    private async saveBatchIfDebug(batch: Record<string, any>[]): Promise<void> {
        if (!this.debugOptions?.saveTransformerCsv || !batch.length) return;

        try {
            const jobName = this.context?.jobName || 'unknownJob';
            const filePath = path.join(process.cwd(), 'staging', `${jobName}_lookup_transformed.csv`);
            await CsvHelper.writeToCsv(batch, filePath, true);
        } catch (error) {
            this.logger?.warn('Failed to save debug CSV', error);
        }
    }
}
