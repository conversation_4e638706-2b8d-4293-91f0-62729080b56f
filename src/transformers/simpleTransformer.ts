import { Transformer, JobContext } from '../core/interfaces';
import { SimpleTransformConfig } from '../config/schema';
import { Logger } from '../utils/logger';
import { ExpressionEvaluator } from '../utils/expressionEvaluator';
import { CsvHelper } from '../utils/csvHelper';
import * as path from 'path';

/**
 * Simple field mapping transformer for ETL v2
 * Consolidated with built-in functions via expression evaluator
 */
export class SimpleTransformer implements Transformer {
    constructor(
        private config: SimpleTransformConfig,
        private context?: JobContext,
        private logger?: Logger,
        private debugOptions?: { saveTransformerCsv?: boolean }
    ) {}

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        const mappings = this.config.mappings;
        const transformedRecords: Record<string, any>[] = [];

        for (const record of records) {
            // Apply filter if specified
            if (this.config.filter) {
                try {
                    const shouldInclude = ExpressionEvaluator.evaluateFilter(
                        this.config.filter, 
                        record, 
                        {
                            globalVariables: this.context?.globalVariables,
                            flowOutputs: this.context?.flowOutputs
                        }
                    );
                    if (!shouldInclude) {
                        this.logger?.debug(`Record filtered out by expression: ${this.config.filter}`);
                        continue;
                    }
                } catch (error) {
                    this.logger?.warn(`Filter expression evaluation failed, including record by default: ${error instanceof Error ? error.message : String(error)}`);
                }
            }

            const transformedRecord: Record<string, any> = {};

            // Check if wildcard mapping exists
            const hasWildcard = mappings['*'] === '*';
            if (hasWildcard) {
                Object.assign(transformedRecord, record);
            }

            // Apply explicit mappings
            for (const [targetField, sourceExpression] of Object.entries(mappings)) {
                if (targetField === '*' && sourceExpression === '*') {
                    continue;
                }

                try {
                    const value = ExpressionEvaluator.evaluate(
                        sourceExpression, 
                        record, 
                        {
                            globalVariables: this.context?.globalVariables,
                            flowOutputs: this.context?.flowOutputs
                        }
                    );
                    transformedRecord[targetField] = value;
                } catch (error) {
                    this.logger?.warn(`Failed to evaluate expression for field ${targetField}: ${error instanceof Error ? error.message : String(error)}`);
                    transformedRecord[targetField] = null;
                }
            }

            transformedRecords.push(transformedRecord);
        }

        // Save debug CSV if enabled
        await this.saveBatchIfDebug(transformedRecords);

        return transformedRecords;
    }

    private async saveBatchIfDebug(batch: Record<string, any>[]): Promise<void> {
        if (!this.debugOptions?.saveTransformerCsv || !batch.length) return;

        try {
            const jobName = this.context?.jobName || 'unknownJob';
            const filePath = path.join(process.cwd(), 'staging', `${jobName}_simple_transformed.csv`);
            await CsvHelper.writeToCsv(batch, filePath, true);
        } catch (error) {
            this.logger?.warn('Failed to save debug CSV', error);
        }
    }
}
