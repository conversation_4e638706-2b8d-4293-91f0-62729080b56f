import { Transformer, JobContext } from '../core/interfaces';
import { SplitTransformConfig } from '../config/schema';
import { Logger } from '../utils/logger';
import { ExpressionEvaluator } from '../utils/expressionEvaluator';

/**
 * Split transformer for splitting records into multiple targets
 * Simplified from the original one-to-many transformer
 */
export class SplitTransformer implements Transformer {
    private targetOutputs = new Map<string, Record<string, any>[]>();

    constructor(
        private config: SplitTransformConfig,
        private context: JobContext,
        private logger: Logger
    ) {
        // Initialize target outputs
        for (const outputTarget of this.config.outputTargets) {
            this.targetOutputs.set(outputTarget.target, []);
        }
    }

    async transform(records: Record<string, any>[]): Promise<Record<string, any>[]> {
        this.logger.info(`Starting split transformation with ${records.length} source records`);

        // Process each source record
        for (const record of records) {
            await this.processRecord(record);
        }

        // Log results
        for (const [targetName, targetRecords] of this.targetOutputs) {
            this.logger.info(`Target ${targetName}: ${targetRecords.length} records generated`);
        }

        // Return combined results
        const allResults: Record<string, any>[] = [];
        for (const targetRecords of this.targetOutputs.values()) {
            allResults.push(...targetRecords);
        }

        this.logger.info(`Split transformation completed: ${allResults.length} total output records`);
        return allResults;
    }

    /**
     * Process a single record and distribute to appropriate targets
     */
    private async processRecord(record: Record<string, any>): Promise<void> {
        for (const outputTarget of this.config.outputTargets) {
            if (this.evaluateCondition(outputTarget.condition, record)) {
                const transformedRecord = await this.applyTargetMappings(record, outputTarget.mappings);
                this.targetOutputs.get(outputTarget.target)!.push(transformedRecord);
            }
        }
    }

    /**
     * Evaluate a condition expression
     */
    private evaluateCondition(condition: string, record: Record<string, any>): boolean {
        if (condition === 'true') return true;
        if (condition === 'false') return false;

        try {
            return ExpressionEvaluator.evaluateFilter(
                condition, 
                record,
                {
                    globalVariables: this.context.globalVariables,
                    flowOutputs: this.context.flowOutputs
                }
            );
        } catch (error) {
            this.logger.warn(`Failed to evaluate condition "${condition}": ${error instanceof Error ? error.message : String(error)}`);
            return false;
        }
    }

    /**
     * Apply field mappings for a specific target
     */
    private async applyTargetMappings(
        record: Record<string, any>,
        mappings: Record<string, string>
    ): Promise<Record<string, any>> {
        const mappedRecord: Record<string, any> = {};

        // Check if wildcard mapping exists
        const hasWildcard = mappings['*'] === '*';
        if (hasWildcard) {
            Object.assign(mappedRecord, record);
        }

        // Apply explicit mappings
        for (const [targetField, sourceExpression] of Object.entries(mappings)) {
            if (targetField === '*' && sourceExpression === '*') {
                continue;
            }

            try {
                const value = ExpressionEvaluator.evaluate(
                    sourceExpression, 
                    record,
                    {
                        globalVariables: this.context.globalVariables,
                        flowOutputs: this.context.flowOutputs
                    }
                );
                mappedRecord[targetField] = value;
            } catch (error) {
                this.logger.warn(`Failed to evaluate mapping for field ${targetField}: ${error instanceof Error ? error.message : String(error)}`);
                mappedRecord[targetField] = null;
            }
        }

        return mappedRecord;
    }

    /**
     * Get target outputs for flow result storage
     */
    getTargetOutputs(): Map<string, Record<string, any>[]> {
        return new Map(this.targetOutputs);
    }
}
