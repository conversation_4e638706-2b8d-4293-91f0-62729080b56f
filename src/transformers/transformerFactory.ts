import { Transformer, JobContext } from "../core/interfaces";
import { TransformConfig } from "../config/schema";
import { Logger } from "../utils/logger";
import { SimpleTransformer } from "./simpleTransformer";
import { JoinTransformer } from "./joinTransformer";
import { SplitTransformer } from "./splitTransformer";
import { LookupTransformer } from "./lookupTransformer";
import { AggregationTransformer } from "./aggregationTransformer";

/**
 * Simplified transformer factory for ETL v2
 * Removed over-engineered built-in transformers and complex pipeline features
 */
export class TransformerFactory {
    constructor(private logger: Logger) {}

    /**
     * Create transformer based on configuration
     */
    createTransformer(
        config: TransformConfig,
        context?: JobContext,
        debugOptions?: { saveTransformerCsv?: boolean }
    ): Transformer {
        this.logger.debug(`Creating transformer of type: ${config.type}`);

        switch (config.type) {
            case "simple":
                return new SimpleTransformer(
                    config,
                    context,
                    this.logger,
                    debugOptions
                );

            case "join":
                if (!context) {
                    throw new Error("Join transformer requires job context");
                }
                return new JoinTransformer(
                    config,
                    context,
                    this.logger,
                    debugOptions
                );

            case "split":
                if (!context) {
                    throw new Error("Split transformer requires job context");
                }
                return new SplitTransformer(config, context, this.logger);

            case "lookup":
                return new LookupTransformer(
                    config,
                    context,
                    this.logger,
                    debugOptions
                );

            case "aggregation":
                return new AggregationTransformer(config, context, this.logger);

            default:
                throw new Error(
                    `Unsupported transformer type: ${(config as any).type}`
                );
        }
    }
}
