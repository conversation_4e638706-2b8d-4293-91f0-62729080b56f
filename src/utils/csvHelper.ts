/**
 * CSV Helper - Consolidated CSV utilities
 * Eliminates code duplication across extractors and transformers
 */
import * as fs from 'fs';

export class CsvHelper {
    /**
     * Convert records to CSV string
     */
    static convertToCsv(records: Record<string, any>[], includeHeader: boolean = true): string {
        if (!records.length) return '';

        const headers = Object.keys(records[0]);
        let csvString = '';

        if (includeHeader) {
            csvString += headers.map(header => this.escapeCsvCell(header)).join(',') + '\n';
        }

        records.forEach(record => {
            csvString += headers.map(header => this.escapeCsvCell(record[header])).join(',') + '\n';
        });

        return csvString;
    }

    /**
     * Escape CSV cell data according to RFC 4180
     */
    static escapeCsvCell(cellData: any): string {
        if (cellData === null || typeof cellData === 'undefined') return '';
        
        const stringData = String(cellData);
        
        // If data contains comma, newline, or double quote, enclose in double quotes
        // and escape existing double quotes by doubling them
        if (stringData.includes(',') || stringData.includes('\n') || stringData.includes('"')) {
            return `"${stringData.replace(/"/g, '""')}"`;
        }
        
        return stringData;
    }

    /**
     * Write records to CSV file
     */
    static async writeToCsv(records: Record<string, any>[], filePath: string, append: boolean = false): Promise<void> {
        if (!records.length) return;

        // Ensure directory exists
        const dir = require('path').dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }

        const csvContent = this.convertToCsv(records, !append || !fs.existsSync(filePath));
        
        if (append) {
            fs.appendFileSync(filePath, csvContent);
        } else {
            fs.writeFileSync(filePath, csvContent);
        }
    }

    /**
     * Read CSV file and parse to records
     */
    static async readFromCsv(filePath: string): Promise<Record<string, any>[]> {
        if (!fs.existsSync(filePath)) {
            throw new Error(`CSV file not found: ${filePath}`);
        }

        const csvContent = fs.readFileSync(filePath, 'utf8');
        const lines = csvContent.trim().split('\n');

        if (lines.length === 0) return [];

        const headers = this.parseCsvLine(lines[0]);
        const records: Record<string, any>[] = [];

        for (let i = 1; i < lines.length; i++) {
            const values = this.parseCsvLine(lines[i]);
            if (values.length === headers.length) {
                const record: Record<string, any> = {};
                for (let j = 0; j < headers.length; j++) {
                    record[headers[j]] = this.parseValue(values[j]);
                }
                records.push(record);
            }
        }

        return records;
    }

    /**
     * Parse a single CSV line handling quotes and escapes
     */
    private static parseCsvLine(line: string): string[] {
        const values: string[] = [];
        let current = '';
        let inQuotes = false;
        let i = 0;

        while (i < line.length) {
            const char = line[i];

            if (char === '"') {
                if (inQuotes && line[i + 1] === '"') {
                    // Escaped quote
                    current += '"';
                    i += 2;
                } else {
                    // Toggle quote state
                    inQuotes = !inQuotes;
                    i++;
                }
            } else if (char === ',' && !inQuotes) {
                // End of field
                values.push(current.trim());
                current = '';
                i++;
            } else {
                current += char;
                i++;
            }
        }

        // Add the last field
        values.push(current.trim());
        return values;
    }

    /**
     * Parse string value to appropriate type
     */
    private static parseValue(value: string): any {
        if (value === '' || value === 'NULL') {
            return null;
        }

        // Try to parse as number
        if (/^-?\d+(\.\d+)?$/.test(value)) {
            return Number(value);
        }

        // Try to parse as boolean
        if (value.toLowerCase() === 'true') return true;
        if (value.toLowerCase() === 'false') return false;

        return value;
    }
}
