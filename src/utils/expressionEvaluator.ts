/**
 * Expression Evaluator - Consolidated expression evaluation
 * Eliminates code duplication across transformers
 */

export class ExpressionEvaluator {
    /**
     * Evaluate a mapping expression
     */
    static evaluate(
        expression: string,
        record: Record<string, any>,
        context?: {
            globalVariables?: Record<string, any>;
            flowOutputs?: Map<string, Record<string, any>[]>;
        }
    ): any {
        // Handle direct field mappings
        if (!expression.startsWith("$")) {
            // Check if it's a dot notation path
            if (expression.includes(".")) {
                return this.getNestedValue(record, expression);
            }
            return record[expression];
        }

        // Handle JavaScript expressions (starting with $)
        return this.evaluateJavaScriptExpression(
            expression.substring(1),
            record,
            context
        );
    }

    /**
     * Evaluate a filter expression (returns boolean)
     */
    static evaluateFilter(
        expression: string,
        record: Record<string, any>,
        context?: {
            globalVariables?: Record<string, any>;
            flowOutputs?: Map<string, Record<string, any>[]>;
        }
    ): boolean {
        // Handle direct field mappings for filter expressions
        if (!expression.startsWith("$")) {
            // For simple field references, check if truthy
            if (expression.includes(".")) {
                const value = this.getNestedValue(record, expression);
                return Boolean(value);
            }
            return Boolean(record[expression]);
        }

        // Handle JavaScript expressions
        const result = this.evaluateJavaScriptExpression(
            expression.substring(1),
            record,
            context
        );
        return Boolean(result);
    }

    /**
     * Get nested property value from an object using dot notation
     */
    static getNestedValue(obj: Record<string, any>, path: string): any {
        const parts = path.split(".");
        let current = obj;

        for (const part of parts) {
            if (current === null || current === undefined) {
                return undefined;
            }

            // Check if this part contains array notation
            const arrayMatch = part.match(/^(.+?)\[(\d+)\]$/);
            if (arrayMatch) {
                const [, fieldName, index] = arrayMatch;
                current = current[fieldName];
                if (Array.isArray(current)) {
                    current = current[parseInt(index, 10)];
                } else {
                    return undefined;
                }
            } else {
                current = current[part];
            }
        }

        return current;
    }

    /**
     * Set nested property value using dot notation
     */
    static setNestedValue(
        obj: Record<string, any>,
        path: string,
        value: any
    ): void {
        const parts = path.split(".");
        let current = obj;

        for (let i = 0; i < parts.length - 1; i++) {
            const part = parts[i];

            if (current[part] === undefined || current[part] === null) {
                current[part] = {};
            }
            current = current[part];
        }

        current[parts[parts.length - 1]] = value;
    }

    /**
     * Build composite key for hash lookups
     */
    static buildCompositeKey(
        record: Record<string, any>,
        fields: string[]
    ): string {
        return fields
            .map((field) => String(record[field] ?? "null"))
            .join("::");
    }

    /**
     * Normalize value for comparison (handles null, undefined, and case)
     */
    static normalizeValue(value: any): string {
        if (value === null || value === undefined) return "null";
        return String(value).toLowerCase().trim();
    }

    /**
     * Convert value to boolean using common conventions
     */
    static toBoolean(value: any): boolean {
        if (typeof value === "boolean") return value;
        if (typeof value === "string") {
            const lower = value.toLowerCase().trim();
            return ["true", "yes", "y", "1", "on"].includes(lower);
        }
        return Boolean(value);
    }

    /**
     * Parse date value handling multiple formats
     */
    static parseDate(value: any): Date | null {
        if (!value) return null;
        const date = new Date(value);
        return isNaN(date.getTime()) ? null : date;
    }

    /**
     * Evaluate JavaScript expression safely
     */
    private static evaluateJavaScriptExpression(
        jsExpression: string,
        record: Record<string, any>,
        context?: {
            globalVariables?: Record<string, any>;
            flowOutputs?: Map<string, Record<string, any>[]>;
        }
    ): any {
        try {
            const evalContext = {
                record,
                globalVariables: context?.globalVariables || {},
                flowOutputs: context?.flowOutputs || new Map(),
                // Safe constructors
                newConstructor: (constructor: any, ...args: any[]) =>
                    new constructor(...args),
                // Built-in objects
                Date,
                Math,
                JSON,
                String,
                Number,
                Boolean,
                Array,
                Object,
                cleanText: BuiltInFunctions.cleanText,
                formatDate: BuiltInFunctions.formatDate,
                formatNumber: BuiltInFunctions.formatNumber,
                truncateText: BuiltInFunctions.truncateText,
            };

            // Simple expression evaluation
            // Note: In production, consider using a safer expression evaluator like vm2
            const func = new Function(
                "record",
                "globalVariables",
                "flowOutputs",
                "newConstructor",
                "Date",
                "Math",
                "JSON",
                "String",
                "Number",
                "Boolean",
                "Array",
                "Object",
                `return ${jsExpression}`
            );

            return func(
                evalContext.record,
                evalContext.globalVariables,
                evalContext.flowOutputs,
                evalContext.newConstructor,
                evalContext.Date,
                evalContext.Math,
                evalContext.JSON,
                evalContext.String,
                evalContext.Number,
                evalContext.Boolean,
                evalContext.Array,
                evalContext.Object,
                evalContext.cleanText,
                evalContext.formatDate,
                evalContext.formatNumber,
                evalContext.truncateText
            );
        } catch (error) {
            throw new Error(
                `Expression evaluation failed: ${
                    error instanceof Error ? error.message : String(error)
                }`
            );
        }
    }
}

/**
 * Built-in helper functions that can be used in expressions
 */
export class BuiltInFunctions {
    /**
     * Clean text by removing extra whitespace and normalizing
     */
    static cleanText(text: string): string {
        if (typeof text !== "string") return text;

        return text
            .trim()
            .replace(/\s+/g, " ")
            .replace(/\r\n/g, "\n")
            .replace(/\r/g, "\n")
            .replace(/\n{3,}/g, "\n\n");
    }

    /**
     * Format date to common formats
     */
    static formatDate(value: any, format: string): string {
        const date = new Date(value);
        if (isNaN(date.getTime())) {
            throw new Error(`Invalid date: ${value}`);
        }

        switch (format) {
            case "ISO":
                return date.toISOString();
            case "YYYY-MM-DD":
                return date.toISOString().split("T")[0];
            case "MM/DD/YYYY":
                return `${(date.getMonth() + 1)
                    .toString()
                    .padStart(2, "0")}/${date
                    .getDate()
                    .toString()
                    .padStart(2, "0")}/${date.getFullYear()}`;
            case "DD/MM/YYYY":
                return `${date.getDate().toString().padStart(2, "0")}/${(
                    date.getMonth() + 1
                )
                    .toString()
                    .padStart(2, "0")}/${date.getFullYear()}`;
            case "timestamp":
                return date.getTime().toString();
            default:
                return value;
        }
    }

    /**
     * Format number to common formats
     */
    static formatNumber(value: any, format: string): number | string {
        const num = Number(value);
        if (isNaN(num)) {
            throw new Error(`Invalid number: ${value}`);
        }

        switch (format) {
            case "integer":
                return Math.round(num);
            case "decimal2":
                return Math.round(num * 100) / 100;
            case "decimal4":
                return Math.round(num * 10000) / 10000;
            case "percentage":
                return (num * 100).toFixed(2) + "%";
            case "currency":
                return (
                    "$" + num.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                );
            case "thousands":
                return num.toLocaleString("en-US");
            default:
                return num;
        }
    }

    /**
     * Truncate text to specified length
     */
    static truncateText(text: string, maxLength: number): string {
        if (typeof text !== "string") return text;
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength - 3) + "...";
    }
}
