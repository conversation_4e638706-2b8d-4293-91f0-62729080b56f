/**
 * Enhanced <PERSON><PERSON> with better formatting and performance
 */
import * as winston from 'winston';

export class Logger {
    private winston: winston.Logger;

    constructor(level: string = process.env.LOG_LEVEL || 'info') {
        this.winston = winston.createLogger({
            level,
            format: winston.format.combine(
                winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
                winston.format.errors({ stack: true }),
                winston.format.printf(({ level, message, timestamp, stack }) => {
                    const logMessage = `${timestamp} [${level.toUpperCase()}] ${message}`;
                    return stack ? `${logMessage}\n${stack}` : logMessage;
                })
            ),
            transports: [
                new winston.transports.Console({
                    format: winston.format.combine(
                        winston.format.colorize(),
                        winston.format.printf(({ level, message, timestamp }) =>
                            `${timestamp} [${level}] ${message}`
                        )
                    )
                }),
                new winston.transports.File({
                    filename: 'combined.log',
                    format: winston.format.combine(
                        winston.format.timestamp(),
                        winston.format.json()
                    )
                })
            ]
        });
    }

    debug(message: string, meta?: any): void {
        this.winston.debug(message, meta);
    }

    info(message: string, meta?: any): void {
        this.winston.info(message, meta);
    }

    warn(message: string, meta?: any): void {
        this.winston.warn(message, meta);
    }

    error(message: string, error?: Error | any): void {
        if (error instanceof Error) {
            this.winston.error(message, { stack: error.stack, error: error.message });
        } else {
            this.winston.error(message, error);
        }
    }

    setLevel(level: string): void {
        this.winston.level = level;
    }

    getLevel(): string {
        return this.winston.level;
    }

    isDebugEnabled(): boolean {
        return this.winston.level === 'debug';
    }

    // Helper methods for ETL operations
    logDataStats(operation: string, count: number, duration?: number): void {
        const message = duration
            ? `${operation}: ${count} records in ${duration}ms (${Math.round(count / (duration / 1000))} records/sec)`
            : `${operation}: ${count} records`;
        this.info(message);
    }

    logBatchProgress(operation: string, processed: number, total: number): void {
        const percentage = Math.round((processed / total) * 100);
        this.info(`${operation}: ${processed}/${total} records (${percentage}%)`);
    }

    logFlowStart(flowName: string): void {
        this.info(`🚀 Starting flow: ${flowName}`);
    }

    logFlowComplete(flowName: string, recordCount: number, duration: number): void {
        this.info(`✅ Completed flow: ${flowName} - ${recordCount} records in ${duration}ms`);
    }

    logFlowError(flowName: string, error: Error): void {
        this.error(`❌ Failed flow: ${flowName}`, error);
    }
}
