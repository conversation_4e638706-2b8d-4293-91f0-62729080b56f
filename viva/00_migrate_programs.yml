# Migrates Program and Benefit related master data for Nonprofit Cloud
# Inherits from base configuration - much cleaner!

extends: "./base_config.yml"

migration:
    name: "Benefit Master Data"
    version: "1.1.0"
    # Define execution order - dependencies matter for relationship resolution
    migrationOrder:
        - "migrate-unitofmeasure" # Step 1: UnitOfMeasure (no dependencies)
        - "create-unitofmeasure-id-map" # Step 2: Create UoM ID mapping
        - "migrate-program-initial" # Step 3: Program (ParentProgramId not resolved yet)
        - "create-program-id-map" # Step 4: Create Program ID mapping
        - "update-program-parent-links" # Step 5: Resolve Program.ParentProgramId
        - "migrate-benefittype" # Step 6: BenefitType (uses UoM mapping)
        - "create-benefittype-id-map" # Step 7: Create BenefitType ID mapping
        - "migrate-benefit" # Step 8: Benefit (uses all mappings)
        - "create-benefit-id-map" # Step 9: Create Benefit ID mapping

# Override connections for this specific migration
connections:
    sourceOrg:
        sfdxAlias: "vicTest" # Different source org for testing
        apiVersion: "63.0"

    targetOrg:
        sfdxAlias: "vicClone" # Different source org for testing
        apiVersion: "63.0"

# Data Source Definitions®∑
dataSources:
    # === SOURCE ORG DATA SOURCES (Extract from Source Salesforce Org) ===

    ds-unitofmeasure:
        name: "ds-unitofmeasure"
        type: "api"
        connection: "sourceOrg"
        object: "UnitOfMeasure"
        fields:
            - "Id"
            - "Name"
            - "CurrencyIsoCode"
            - "UnitCode"
            - "Description"
            - "Type"
        batchSize: 1000

    ds-program:
        name: "ds-program"
        type: "api"
        connection: "sourceOrg"
        object: "Program"
        fields:
            - "Id"
            - "Name"
            - "CurrencyIsoCode"
            - "StartDate"
            - "EndDate"
            - "Status"
            - "Summary"
            - "AdditionalContext"
            - "TotalEnrolleeCount"
            - "ActiveEnrolleeCount"
            - "CurrentMonthDisbCount"
            - "CurrentYearDisbCount"
            - "PreviousMonthDisbCount"
            - "PreviousYearDisbCount"
            - "UsageType"
            - "ParentProgramId"
            - "Maximum_Number_of_People__c"
            - "Maximum_Total_Hours__c"
            - "Maximum_Total_Payment__c"
        # whereClause: "Viva_ID__c != null"
        batchSize: 500

    ds-benefittype:
        name: "ds-benefittype"
        type: "api"
        connection: "sourceOrg"
        object: "BenefitType"
        fields:
            - "Id"
            - "Name"
            - "CurrencyIsoCode"
            - "Category"
            - "Description"
            - "ProcessType"
            - "UnitofMeasureId"
            - "Type"
            - "ImageSource"
        batchSize: 1000

    ds-benefit:
        name: "ds-benefit"
        type: "api"
        connection: "sourceOrg"
        object: "Benefit"
        fields:
            - "Id"
            - "Name"
            - "CurrencyIsoCode"
            - "BenefitTypeId"
            - "UnitId"
            - "IsActive"
            - "StartDateTime"
            - "EndDateTime"
            - "BenefitStatus"
            - "Description"
            - "ProgramId"
            - "Funding_Agency__c"
            - "Funding_Pool_Code__c"
            - "Funding_Pool_Description__c"
            - "Funding_Pool_Name__c"
            - "RC_Code__c"
            - "Scheme__c"
            - "Scheme_Type__c"
        batchSize: 500

    # === TARGET ORG DATA SOURCES (Query Migrated Records for ID Mapping) ===

    migrated-unitofmeasure:
        name: "migrated-unitofmeasure"
        type: "api"
        connection: "targetOrg"
        object: "UnitOfMeasure"
        fields: ["Id", "Viva_ID__c"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

    migrated-program:
        name: "migrated-program"
        type: "api"
        connection: "targetOrg"
        object: "Program"
        fields: ["Id", "Viva_ID__c", "Name"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

    migrated-benefittype:
        name: "migrated-benefittype"
        type: "api"
        connection: "targetOrg"
        object: "BenefitType"
        fields: ["Id", "Viva_ID__c"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

    migrated-benefits:
        name: "migrated-benefits"
        type: "api"
        connection: "targetOrg"
        object: "Benefit"
        fields: ["Id", "ProgramId", "Funding_Pool_Name__c", "Name"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

    # === CSV DATA SOURCES (Inherits common ones from base, adds specific ones) ===

    map-csv-unitofmeasure-ids:
        name: "map-csv-unitofmeasure-ids"
        type: "csv"
        filePath: "./data/unitofmeasure_id_map.csv"

    map-csv-program-ids:
        name: "map-csv-program-ids"
        type: "csv"
        filePath: "./data/program_id_map.csv"

    map-csv-benefittype-ids:
        name: "map-csv-benefittype-ids"
        type: "csv"
        filePath: "./data/benefittype_id_map.csv"

    map-csv-benefit-ids:
        name: "map-csv-benefit-ids"
        type: "csv"
        filePath: "./data/benefit_id_map.csv"

# Flow Definitions
flows:
    # === PHASE 1: UNITOFMEASURE MIGRATION & MAPPING ===

    migrate-unitofmeasure:
        name: "1. Migrate UnitOfMeasure"
        type: "simple"
        saveJobResults: true
        sources:
            - name: "ds-unitofmeasure"
        transform:
            type: "simple"
            mappings:
                Viva_ID__c: "Id"
                Name: "Name"
                CurrencyIsoCode: "CurrencyIsoCode"
                UnitCode: "UnitCode"
                Description: "Description"
                Type: "Type"
        targets:
            - name: "target-unitofmeasure"
              object: "UnitOfMeasure"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 2000
        jobResultsPath: "./staging/job_results/unitofmeasure_migration.csv"

    create-unitofmeasure-id-map:
        name: "2. Create UnitOfMeasure ID Map"
        type: "simple"
        dependencies: ["migrate-unitofmeasure"]
        sources:
            - name: "migrated-unitofmeasure"
        transform:
            type: "simple"
            mappings:
                Source_UoM_ID: "Viva_ID__c"
                Target_UoM_ID: "Id"
        targets:
            - name: "uom-id-map-csv-export"
              loadStrategy: "csv-export"
              filePath: "./data/unitofmeasure_id_map.csv"

    # === PHASE 2: PROGRAM MIGRATION & MAPPING (Two-Step for ParentProgramId) ===

    migrate-program-initial:
        name: "3. Migrate Program Initial Data"
        type: "simple"
        saveJobResults: true
        sources:
            - name: "ds-program"
        transform:
            type: "simple"
            mappings:
                Viva_ID__c: "Id"
                Name: "Name"
                CurrencyIsoCode: "CurrencyIsoCode"
                StartDate: "StartDate"
                EndDate: "EndDate"
                Status: "Status"
                Summary: "Summary"
                AdditionalContext: "AdditionalContext"
                TotalEnrolleeCount: "TotalEnrolleeCount"
                ActiveEnrolleeCount: "ActiveEnrolleeCount"
                CurrentMonthDisbCount: "CurrentMonthDisbCount"
                CurrentYearDisbCount: "CurrentYearDisbCount"
                PreviousMonthDisbCount: "PreviousMonthDisbCount"
                PreviousYearDisbCount: "PreviousYearDisbCount"
                UsageType: "UsageType"
                Maximum_Number_of_People__c: "Maximum_Number_of_People__c"
                Maximum_Total_Hours__c: "Maximum_Total_Hours__c"
                Maximum_Total_Payment__c: "Maximum_Total_Payment__c"
                # ParentProgramId will be resolved in update step
        targets:
            - name: "target-program-initial"
              object: "Program"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 1000
        jobResultsPath: "./staging/job_results/program_initial_migration.csv"

    create-program-id-map:
        name: "4. Create Program ID Map"
        type: "simple"
        dependencies: ["migrate-program-initial"]
        sources:
            - name: "migrated-program"
        transform:
            type: "simple"
            mappings:
                Source_Program_ID: "Viva_ID__c"
                Target_Program_ID: "Id"
                Target_Program_Name: "Name"
        targets:
            - name: "program-id-map-csv-export"
              loadStrategy: "csv-export"
              filePath: "./data/program_id_map.csv"

    update-program-parent-links:
        name: "5. Update Program Parent Links"
        type: "join"
        saveJobResults: true
        dependencies: ["migrate-program-initial", "create-program-id-map"]
        sources:
            - name: "ds-program"
              alias: "orig_prog"
            - name: "map-csv-program-ids"
              alias: "prog_map"
            - name: "map-csv-program-ids"
              alias: "parent_map"
        transform:
            type: "join"
            primarySource: "orig_prog"
            joins:
                # Join to get the Target ID of the Program record to update
                - sourceAlias: "prog_map"
                  joinType: "left"
                  on:
                      - primaryField: "Id"
                        joinField: "Source_Program_ID"
                # Join to resolve ParentProgramId if it exists
                - sourceAlias: "parent_map"
                  joinType: "left"
                  on:
                      - primaryField: "ParentProgramId"
                        joinField: "Source_Program_ID"
            mappings:
                Id: "prog_map_Target_Program_ID"
                ParentProgramId: "$record.orig_prog_ParentProgramId ? record.parent_map_Target_Program_ID : null"
        targets:
            - name: "target-program-parent-update"
              object: "Program"
              connection: "targetOrg"
              operation: "update"
              loadStrategy: "bulk2"
              batchSize: 1000
        jobResultsPath: "./staging/job_results/program_parent_update.csv"
        debug:
            saveTransformerCsv: true
            saveExtractorCsv: true

    # === PHASE 3: BENEFITTYPE MIGRATION & MAPPING ===

    migrate-benefittype:
        name: "6. Migrate BenefitType"
        type: "join"
        saveJobResults: true
        dependencies: ["migrate-unitofmeasure", "create-unitofmeasure-id-map"]
        sources:
            - name: "ds-benefittype"
              alias: "bt"
            - name: "map-csv-unitofmeasure-ids"
              alias: "uom_map"
        transform:
            type: "join"
            primarySource: "bt"
            joins:
                - sourceAlias: "uom_map"
                  joinType: "left"
                  on:
                      - primaryField: "UnitofMeasureId"
                        joinField: "Source_UoM_ID"
            mappings:
                Viva_ID__c: "bt_Id"
                Name: "bt_Name"
                UnitofMeasureId: "$record.bt_UnitofMeasureId && record.uom_map_Target_UoM_ID ? record.uom_map_Target_UoM_ID : null"
                CurrencyIsoCode: "bt_CurrencyIsoCode"
                Category: "bt_Category"
                Description: "bt_Description"
                ProcessType: "bt_ProcessType"
                Type: "bt_Type"
                ImageSource: "bt_ImageSource"
        targets:
            - name: "target-benefittype"
              object: "BenefitType"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 2000
        jobResultsPath: "./staging/job_results/benefittype_migration.csv"
        debug:
            saveTransformerCsv: true
            saveExtractorCsv: true

    create-benefittype-id-map:
        name: "7. Create BenefitType ID Map"
        type: "simple"
        dependencies: ["migrate-benefittype"]
        sources:
            - name: "migrated-benefittype"
        transform:
            type: "simple"
            mappings:
                Source_BenefitType_ID: "Viva_ID__c"
                Target_BenefitType_ID: "Id"
        targets:
            - name: "benefittype-id-map-csv-export"
              loadStrategy: "csv-export"
              filePath: "./data/benefittype_id_map.csv"

    # === PHASE 4: BENEFIT MIGRATION (uses all mappings) ===

    migrate-benefit:
        name: "8. Migrate Benefit"
        type: "join"
        saveJobResults: true
        dependencies:
            - "update-program-parent-links"
            - "create-program-id-map"
            - "create-benefittype-id-map"
            - "create-unitofmeasure-id-map"
        sources:
            - name: "ds-benefit"
              alias: "b"
            - name: "map-csv-program-ids"
              alias: "prog_map"
            - name: "map-csv-benefittype-ids"
              alias: "bt_map"
            - name: "map-csv-unitofmeasure-ids"
              alias: "uom_map"
        transform:
            type: "join"
            primarySource: "b"
            joins:
                - sourceAlias: "prog_map"
                  joinType: "left"
                  on:
                      - primaryField: "ProgramId"
                        joinField: "Source_Program_ID"
                - sourceAlias: "bt_map"
                  joinType: "left"
                  on:
                      - primaryField: "BenefitTypeId"
                        joinField: "Source_BenefitType_ID"
                - sourceAlias: "uom_map"
                  joinType: "left"
                  on:
                      - primaryField: "UnitId"
                        joinField: "Source_UoM_ID"
            mappings:
                Viva_ID__c: "b_Id"
                Name: "b_Name"
                ProgramId: "$record.b_ProgramId && record.prog_map_Target_Program_ID ? record.prog_map_Target_Program_ID : null"
                BenefitTypeId: "$record.b_BenefitTypeId && record.bt_map_Target_BenefitType_ID ? record.bt_map_Target_BenefitType_ID : null"
                UnitId: "$record.b_UnitId && record.uom_map_Target_UoM_ID ? record.uom_map_Target_UoM_ID : null"
                CurrencyIsoCode: "b_CurrencyIsoCode"
                IsActive: "b_IsActive"
                StartDateTime: "b_StartDateTime"
                EndDateTime: "b_EndDateTime"
                BenefitStatus: "b_BenefitStatus"
                Description: "b_Description"
                Funding_Agency__c: "b_Funding_Agency__c"
                Funding_Pool_Code__c: "b_Funding_Pool_Code__c"
                Funding_Pool_Description__c: "b_Funding_Pool_Description__c"
                Funding_Pool_Name__c: "b_Funding_Pool_Name__c"
                RC_Code__c: "b_RC_Code__c"
                Scheme__c: "b_Scheme__c"
                Scheme_Type__c: "b_Scheme_Type__c"
        targets:
            - name: "target-benefit"
              object: "Benefit"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 1000
        debug:
            saveTransformerCsv: true
            saveExtractorCsv: true
        jobResultsPath: "./staging/job_results/benefit_migration.csv"

    create-benefit-id-map:
        name: "9. Create Benefit ID Map"
        type: "simple"
        dependencies: ["migrate-benefit"]
        sources:
            - name: "migrated-benefits"

        transform:
            type: "simple"
            mappings:
                Benefit_Funding_Pool_Name__c: "Funding_Pool_Name__c"
                Source_Benefit_Name: "Name"
                Target_Benefit_ID: "Id"
                ProgramId: "ProgramId"
        targets:
            - name: "benefit-id-map-csv-export"
              loadStrategy: "csv-export"
              filePath: "./data/benefit_id_map.csv"
