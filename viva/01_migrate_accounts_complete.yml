# Combined migration: Accounts + ID Mapping + Payment Instruments
extends: "./vha_base_config.yml"

migration:
    name: "Stage 1 - Complete Account Migration (Accounts + Payment Instruments)"
    version: "2.0.0"
    migrationOrder:
        - "migrate_all_source_accounts1"
        - "migrate_all_source_accounts2"
        - "migrate_all_source_accounts3"
        - "generate_account_id_csv_map"
        - "create_payment_instruments1"
        - "create_payment_instruments2"
        - "create_payment_instruments3"

dataSources:
    source_AllAccounts_S1:
        type: csv
        filePath: "./fc/simple_account_1.csv"
    source_AllAccounts_S2:
        type: csv
        filePath: "./fc/simple_account_2.csv"
    source_AllAccounts_S3:
        type: csv
        filePath: "./fc/simple_account_3.csv"

    target_accounts_for_mapping:
        type: api
        connection: targetOrg
        object: Account
        fields:
            - Id
            - Viva_ID__c
        whereClause: "Viva_ID__c != null"
        recordLimit: 1000000 # Consider if this limit is feasible for API extraction
        batchSize: 10000 # High batchSize for API, ensure connection supports it

    account_id_map_csv:
        type: csv
        filePath: "./data/account_id_mapping.csv"

flows:
    # === PHASE 1: MIGRATE ACCOUNTS ===
    migrate_all_source_accounts1:
        name: "S1.1 - Migrate Source Accounts to Target Org (File 1)"
        sources:
            - name: source_AllAccounts_S1
              # alias: sourceAcc1 # Alias is optional for single-source simple transforms
        transform:
            type: simple # Assuming simple transform as there are no explicit joins here
            mappings:
                Viva_ID__c: $Id # $Id from source_AllAccounts_S1
                RecordTypeId: "$globalVariables.accountRecordTypeMap[$RecordTypeId] || $RecordTypeId"
                FirstName: "$IsPersonAccount ? $FirstName : null"
                LastName: "$IsPersonAccount ? $LastName : null"
                MiddleName: "$IsPersonAccount ? $Middle_Name_s__pc : null"
                PersonTitle: "$IsPersonAccount ? $PersonTitle : null"
                Salutation: "$IsPersonAccount ? $Salutation : null"
                PersonEmail: "$IsPersonAccount ? ($PersonEmail || $Personal_Email__pc) : null"
                PersonMobilePhone: "$IsPersonAccount ? $PersonMobilePhone : null"
                Phone: $Phone
                BillingStreet: "$IsPersonAccount ? $PersonMailingStreet : null"
                BillingCity: "$IsPersonAccount ? $PersonMailingCity : null"
                BillingPostalCode: "$IsPersonAccount ? $PersonMailingPostalCode : null"
                BillingCountry: "$IsPersonAccount ? $PersonMailingCountry : null"
                CreatedDate: "$CreatedDate ? new Date(parseInt($CreatedDate)).toISOString() : null"
                Viva_Modified_Date__c: "$LastModifiedDate ? new Date(parseInt($LastModifiedDate)).toISOString() : null"
                Ethnicity__c: "$IsPersonAccount ? $Ethnicity__pc : null"
                Iwi__pc: "$IsPersonAccount ? $Iwi__pc : null"
                PersonBirthdate: "$IsPersonAccount && $PersonBirthdate ? new Date(parseInt($PersonBirthdate)).toISOString() : null"
                Viva_Client_Number__pc: "$IsPersonAccount ? $Client_ID_Number__pc : null"
                Preferred_Name__c: "$IsPersonAccount ? $Preferred_Name__pc : null"
                PersonGenderIdentity: "$IsPersonAccount ? $Gender__pc : null"
                Deceased__c: "$RecordTypeId === $globalVariables.deceasedVictimSourceRtId ? true : false"
                Alert_Notes__c: >-
                    $truncateText($([
                      ($Red_Flag__pc ? 'Red Flag: ' + $Red_Flag__pc : ''),
                      ($RedFlagLastModifed__c ? 'Red Flag Last Modified: ' + $RedFlagLastModifed__c : ''),
                      ($RedFlagModifiedBy__c ? 'Red Flag Modified By: ' + $RedFlagModifiedBy__c : ''),
                      ($Risk_Assessment_Notes__pc ? 'Risk Assessment: ' + $Risk_Assessment_Notes__pc : ''),
                      ($Additional_SV_Information__c ? 'Additional Info: ' + $Additional_SV_Information__c : ''),
                      ($International_Payments_Comments__pc ? 'Intl Payments Comments: ' + $International_Payments_Comments__pc : ''),
                      ($Payment_Notes__pc ? 'Payment Notes: ' + $Payment_Notes__pc : '')
                    ].filter(Boolean).join('\\n\\n')), 255)
                Description: >-
                    $([
                      ($Additional_Office_Information__c ? 'Office Info: ' + $Additional_Office_Information__c : ''),
                      ($Age__pc ? 'Age: ' + $Age__pc : '')
                    ].filter(Boolean).join('\\n\\n'))
        targets:
            - name: targetAccountsS1 # Name suggests specific target, but it's just Account object
              object: Account
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_1_accounts_migration_results.csv"

    migrate_all_source_accounts2:
        name: "S1.2 - Migrate Source Accounts to Target Org (File 2)"
        sources:
            - name: source_AllAccounts_S2
        transform:
            type: simple # Assuming simple transform
            mappings:
                Viva_ID__c: $Id
                RecordTypeId: "$globalVariables.accountRecordTypeMap[$RecordTypeId] || $RecordTypeId"
                FirstName: "$IsPersonAccount ? $FirstName : null"
                LastName: "$IsPersonAccount ? $LastName : null"
                MiddleName: "$IsPersonAccount ? $Middle_Name_s__pc : null"
                PersonTitle: "$IsPersonAccount ? $PersonTitle : null"
                Salutation: "$IsPersonAccount ? $Salutation : null"
                PersonEmail: "$IsPersonAccount ? ($PersonEmail || $Personal_Email__pc) : null"
                PersonMobilePhone: "$IsPersonAccount ? $PersonMobilePhone : null"
                Phone: $Phone
                BillingStreet: "$IsPersonAccount ? $PersonMailingStreet : null"
                BillingCity: "$IsPersonAccount ? $PersonMailingCity : null"
                BillingPostalCode: "$IsPersonAccount ? $PersonMailingPostalCode : null"
                BillingCountry: "$IsPersonAccount ? $PersonMailingCountry : null"
                CreatedDate: "$CreatedDate ? new Date(parseInt($CreatedDate)).toISOString() : null"
                Viva_Modified_Date__c: "$LastModifiedDate ? new Date(parseInt($LastModifiedDate)).toISOString() : null"
                Ethnicity__c: "$IsPersonAccount ? $Ethnicity__pc : null"
                Iwi__pc: "$IsPersonAccount ? $Iwi__pc : null"
                PersonBirthdate: "$IsPersonAccount && $PersonBirthdate ? new Date(parseInt($PersonBirthdate)).toISOString() : null"
                Viva_Client_Number__pc: "$IsPersonAccount ? $Client_ID_Number__pc : null"
                Preferred_Name__c: "$IsPersonAccount ? $Preferred_Name__pc : null"
                PersonGenderIdentity: "$IsPersonAccount ? $Gender__pc : null"
                Deceased__c: "$RecordTypeId === $globalVariables.deceasedVictimSourceRtId ? true : false"
                Alert_Notes__c: >-
                    $truncateText($([
                      ($Red_Flag__pc ? 'Red Flag: ' + $Red_Flag__pc : ''),
                      ($RedFlagLastModifed__c ? 'Red Flag Last Modified: ' + $RedFlagLastModifed__c : ''),
                      ($RedFlagModifiedBy__c ? 'Red Flag Modified By: ' + $RedFlagModifiedBy__c : ''),
                      ($Risk_Assessment_Notes__pc ? 'Risk Assessment: ' + $Risk_Assessment_Notes__pc : ''),
                      ($Additional_SV_Information__c ? 'Additional Info: ' + $Additional_SV_Information__c : ''),
                      ($International_Payments_Comments__pc ? 'Intl Payments Comments: ' + $International_Payments_Comments__pc : ''),
                      ($Payment_Notes__pc ? 'Payment Notes: ' + $Payment_Notes__pc : '')
                    ].filter(Boolean).join('\\n\\n')), 255)
                Description: >-
                    $([
                      ($Additional_Office_Information__c ? 'Office Info: ' + $Additional_Office_Information__c : ''),
                      ($Age__pc ? 'Age: ' + $Age__pc : '')
                    ].filter(Boolean).join('\\n\\n'))
        targets:
            - name: targetAccountsS2 # Changed target name to be unique for clarity, though loads to same object
              object: Account
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_2_accounts_migration_results.csv"

    migrate_all_source_accounts3:
        name: "S1.3 - Migrate Source Accounts to Target Org (File 3)"
        sources:
            - name: source_AllAccounts_S3
        transform:
            type: simple # Assuming simple transform
            mappings:
                Viva_ID__c: $Id
                RecordTypeId: "$globalVariables.accountRecordTypeMap[$RecordTypeId] || $RecordTypeId"
                FirstName: "$IsPersonAccount ? $FirstName : null"
                LastName: "$IsPersonAccount ? $LastName : null"
                MiddleName: "$IsPersonAccount ? $Middle_Name_s__pc : null"
                PersonTitle: "$IsPersonAccount ? $PersonTitle : null"
                Salutation: "$IsPersonAccount ? $Salutation : null"
                PersonEmail: "$IsPersonAccount ? ($PersonEmail || $Personal_Email__pc) : null"
                PersonMobilePhone: "$IsPersonAccount ? $PersonMobilePhone : null"
                Phone: $Phone
                BillingStreet: "$IsPersonAccount ? $PersonMailingStreet : null"
                BillingCity: "$IsPersonAccount ? $PersonMailingCity : null"
                BillingPostalCode: "$IsPersonAccount ? $PersonMailingPostalCode : null"
                BillingCountry: "$IsPersonAccount ? $PersonMailingCountry : null"
                CreatedDate: "$CreatedDate ? new Date(parseInt($CreatedDate)).toISOString() : null"
                Viva_Modified_Date__c: "$LastModifiedDate ? new Date(parseInt($LastModifiedDate)).toISOString() : null"
                Ethnicity__c: "$IsPersonAccount ? $Ethnicity__pc : null"
                Iwi__pc: "$IsPersonAccount ? $Iwi__pc : null"
                PersonBirthdate: "$IsPersonAccount && $PersonBirthdate ? new Date(parseInt($PersonBirthdate)).toISOString() : null"
                Viva_Client_Number__pc: "$IsPersonAccount ? $Client_ID_Number__pc : null"
                Preferred_Name__c: "$IsPersonAccount ? $Preferred_Name__pc : null"
                PersonGenderIdentity: "$IsPersonAccount ? $Gender__pc : null"
                Deceased__c: "$RecordTypeId === $globalVariables.deceasedVictimSourceRtId ? true : false"
                Alert_Notes__c: >-
                    $truncateText($([
                      ($Red_Flag__pc ? 'Red Flag: ' + $Red_Flag__pc : ''),
                      ($RedFlagLastModifed__c ? 'Red Flag Last Modified: ' + $RedFlagLastModifed__c : ''),
                      ($RedFlagModifiedBy__c ? 'Red Flag Modified By: ' + $RedFlagModifiedBy__c : ''),
                      ($Risk_Assessment_Notes__pc ? 'Risk Assessment: ' + $Risk_Assessment_Notes__pc : ''),
                      ($Additional_SV_Information__c ? 'Additional Info: ' + $Additional_SV_Information__c : ''),
                      ($International_Payments_Comments__pc ? 'Intl Payments Comments: ' + $International_Payments_Comments__pc : ''),
                      ($Payment_Notes__pc ? 'Payment Notes: ' + $Payment_Notes__pc : '')
                    ].filter(Boolean).join('\\n\\n')), 255)
                Description: >-
                    $([
                      ($Additional_Office_Information__c ? 'Office Info: ' + $Additional_Office_Information__c : ''),
                      ($Age__pc ? 'Age: ' + $Age__pc : '')
                    ].filter(Boolean).join('\\n\\n'))
        targets:
            - name: targetAccountsS3 # Changed target name
              object: Account
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_3_accounts_migration_results.csv"

    # === PHASE 2: GENERATE ACCOUNT ID MAPPING ===
    generate_account_id_csv_map:
        name: "S1.4 - Generate Account ID Mapping CSV"
        dependencies:
            [
                "migrate_all_source_accounts1",
                "migrate_all_source_accounts2",
                "migrate_all_source_accounts3",
            ]
        sources:
            - name: target_accounts_for_mapping # This is API source from Salesforce
              alias: targetSf
            - name: source_AllAccounts_S1 # Joining with first CSV source file only for PersonContactId
              alias: sourceCsvS1
        transform:
            type: "join"
            primarySource: "targetSf"
            joins:
                - sourceAlias: "sourceCsvS1"
                  joinType: "left"
                  on:
                      - primaryField: "Viva_ID__c" # targetSf.Viva_ID__c
                        joinField: "Id" # sourceCsvS1.Id
            mappings:
                Source_Account_Id: "$targetSf.Viva_ID__c"
                Target_Account_Id: "$targetSf.Id"
                Source_PersonContactId: "$sourceCsvS1.PersonContactId || null" # This comes from source_AllAccounts_S1 only
        targets:
            - name: accountIdMapCsvExport
              loadStrategy: csv-export
              filePath: "./data/account_id_mapping.csv"
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_4_account_mapping_results.csv" # Corrected job number

    # === PHASE 3: CREATE PAYMENT INSTRUMENTS ===
    create_payment_instruments1:
        name: "S1.5 - Create Payment Instruments (File 1)"
        dependencies: ["generate_account_id_csv_map"]
        sources:
            - name: "source_AllAccounts_S1"
              alias: "sourceAcc"
            - name: "account_id_map_csv"
              alias: "accMap"
        transform:
            type: "join"
            primarySource: "sourceAcc"
            joins:
                - sourceAlias: "accMap"
                  joinType: "inner"
                  on:
                      - primaryField: "Id" # sourceAcc.Id
                        joinField: "Source_Account_Id" # accMap.Source_Account_Id
            mappings:
                AccountId: "$accMap.Target_Account_Id"
                AccountHolderName: "$sourceAcc.Account_Name__pc"
                Bank_Account__c: "$sourceAcc.Account_Number_Account__pc"
                Bank_Number__c: "$sourceAcc.Account_Number_Bank__pc || null"
                Bank_Branch__c: "$sourceAcc.Account_Number_Branch__pc || null"
                Bank_Suffix__c: "$sourceAcc.Account_Number_Suffix__pc || null"
                International_Bank_Account__c: "$sourceAcc.Bank_Account__pc || null"
                International_Branch_Address__c: "$sourceAcc.Bank_Address__pc || null"
                BSB__c: "$sourceAcc.BSB__pc || null"
                Currency_to_Pay__c: "$sourceAcc.Currency__pc || null"
                IBAN__c: "$sourceAcc.IBAN__pc || null"
                International_Payee_Address__c: "$sourceAcc.International_Payee_Address__pc || null"
                International_Payee_Full_Name__c: "$sourceAcc.International_Payee_Full_Name__pc || null"
                International_Payment_Comments__c: "$sourceAcc.Payment_Notes__pc || $sourceAcc.International_Payments_Comments__pc || null"
                BankName: "$sourceAcc.Name_Of_Bank__pc || null"
                Swift_BIC__c: "$sourceAcc.SWIFT__pc || null"
                Viva_ID__c: "$sourceAcc.Id" # This will be external ID for PaymentInstrument, derived from source Account Id
                Status__c: "$'Active'"
                Type: "$sourceAcc.International_Payee_Full_Name__pc ? 'SEPA Direct Debit' : null"
        targets:
            - name: "target_payment_instruments_s1" # Unique target name for clarity
              object: "PaymentInstrument"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 1000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_5_payment_instruments_creation.csv"

    create_payment_instruments2:
        name: "S1.6 - Create Payment Instruments (File 2)"
        dependencies: ["generate_account_id_csv_map"]
        sources:
            - name: "source_AllAccounts_S2"
              alias: "sourceAcc"
            - name: "account_id_map_csv"
              alias: "accMap"
        transform:
            type: "join"
            primarySource: "sourceAcc"
            joins:
                - sourceAlias: "accMap"
                  joinType: "inner"
                  on:
                      - primaryField: "Id"
                        joinField: "Source_Account_Id"
            mappings:
                AccountId: "$accMap.Target_Account_Id"
                AccountHolderName: "$sourceAcc.Account_Name__pc"
                Bank_Account__c: "$sourceAcc.Account_Number_Account__pc"
                Bank_Number__c: "$sourceAcc.Account_Number_Bank__pc || null"
                Bank_Branch__c: "$sourceAcc.Account_Number_Branch__pc || null"
                Bank_Suffix__c: "$sourceAcc.Account_Number_Suffix__pc || null"
                International_Bank_Account__c: "$sourceAcc.Bank_Account__pc || null"
                International_Branch_Address__c: "$sourceAcc.Bank_Address__pc || null"
                BSB__c: "$sourceAcc.BSB__pc || null"
                Currency_to_Pay__c: "$sourceAcc.Currency__pc || null"
                IBAN__c: "$sourceAcc.IBAN__pc || null"
                International_Payee_Address__c: "$sourceAcc.International_Payee_Address__pc || null"
                International_Payee_Full_Name__c: "$sourceAcc.International_Payee_Full_Name__pc || null"
                International_Payment_Comments__c: "$sourceAcc.Payment_Notes__pc || $sourceAcc.International_Payments_Comments__pc || null"
                BankName: "$sourceAcc.Name_Of_Bank__pc || null"
                Swift_BIC__c: "$sourceAcc.SWIFT__pc || null"
                Viva_ID__c: "$sourceAcc.Id"
                Status__c: "$'Active'"
                Type: "$sourceAcc.International_Payee_Full_Name__pc ? 'SEPA Direct Debit' : null"
        targets:
            - name: "target_payment_instruments_s2"
              object: "PaymentInstrument"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 1000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_6_payment_instruments_creation.csv"

    create_payment_instruments3:
        name: "S1.7 - Create Payment Instruments (File 3)"
        dependencies: ["generate_account_id_csv_map"]
        sources:
            - name: "source_AllAccounts_S3"
              alias: "sourceAcc"
            - name: "account_id_map_csv"
              alias: "accMap"
        transform:
            type: "join"
            primarySource: "sourceAcc"
            joins:
                - sourceAlias: "accMap"
                  joinType: "inner"
                  on:
                      - primaryField: "Id"
                        joinField: "Source_Account_Id"
            mappings:
                AccountId: "$accMap.Target_Account_Id"
                AccountHolderName: "$sourceAcc.Account_Name__pc"
                Bank_Account__c: "$sourceAcc.Account_Number_Account__pc"
                Bank_Number__c: "$sourceAcc.Account_Number_Bank__pc || null"
                Bank_Branch__c: "$sourceAcc.Account_Number_Branch__pc || null"
                Bank_Suffix__c: "$sourceAcc.Account_Number_Suffix__pc || null"
                International_Bank_Account__c: "$sourceAcc.Bank_Account__pc || null"
                International_Branch_Address__c: "$sourceAcc.Bank_Address__pc || null"
                BSB__c: "$sourceAcc.BSB__pc || null"
                Currency_to_Pay__c: "$sourceAcc.Currency__pc || null"
                IBAN__c: "$sourceAcc.IBAN__pc || null"
                International_Payee_Address__c: "$sourceAcc.International_Payee_Address__pc || null"
                International_Payee_Full_Name__c: "$sourceAcc.International_Payee_Full_Name__pc || null"
                International_Payment_Comments__c: "$sourceAcc.Payment_Notes__pc || $sourceAcc.International_Payments_Comments__pc || null"
                BankName: "$sourceAcc.Name_Of_Bank__pc || null"
                Swift_BIC__c: "$sourceAcc.SWIFT__pc || null"
                Viva_ID__c: "$sourceAcc.Id"
                Status__c: "$'Active'"
                Type: "$sourceAcc.International_Payee_Full_Name__pc ? 'SEPA Direct Debit' : null"
        targets:
            - name: "target_payment_instruments_s3"
              object: "PaymentInstrument"
              connection: "targetOrg"
              operation: "upsert"
              externalId: "Viva_ID__c"
              loadStrategy: "bulk2"
              batchSize: 1000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S01_7_payment_instruments_creation.csv"
