# Enhanced Case Migration with Multiple CSV File Support
extends: "./vha_base_config.yml"

migration:
    name: "Stage 4 - Complete Case Migration (Multi-File Support)"
    version: "2.1.0"
    continueOnError: true
    migrationOrder:
        # Phase 1: Incident Cases and Referrals
        - "migrate_incident_cases"
        - "create_incident_case_id_mapping"
        - "migrate_referrals"

        # Phase 2: Support Event Cases
        - "migrate_support_event_cases"
        - "create_support_event_case_id_mapping"

        # Phase 3: Case Participants
        - "migrate_victim_assignments_to_case_participants"
        - "migrate_incident_roles_to_case_participants"

    globalVariables:
        referralType: "INBOUND"
        defaultStatus: "Active"
        roleMap:
            "Aunt of Primary Victim": "Whānau"
            "Caregiver": "Whānau"
            "Colleague of Primary Victim": "Whānau"
            "Cousin of Primary Victim": "Whānau"
            "Daughter in law of Primary Victim": "Whānau"
            "Daughter of Deceased": "Whānau"
            "Daughter of Primary Victim": "Whān<PERSON>"
            "Deceased person": "Other"
            "Discoverer (Non-related)": "Discoverer"
            "Discoverer (Related - Immediate)": "Discoverer"
            "Discoverer (Related - Other)": "Discoverer"
            "Employer of Primary Victim": "Employer"
            "Ex-partner of Deceased": "Whānau"
            "Ex-partner of Primary Victim": "Whānau"
            "Family Spokesperson": "Whānau"
            "First on Scene": "First on Scene"
            "Flatmate": "Whānau"
            "Foster Parent": "Whānau"
            "Friend of Deceased": "Whānau"
            "Friend of Primary Victim": "Whānau"
            "Grandchild of Primary Victim": "Whānau"
            "Grandparent of Primary Victim": "Whānau"
            "Great Grand-Parent of Primary Victim": "Whānau"
            "Legal Guardian": "Whānau"
            "Mutual Participant": "Mutual Participant"
            "Neighbour of Primary Victim": "Other affected party"
            "Nephew of Primary Victim": "Whānau"
            "Niece of Primary Victim": "Whānau"
            "Parent in law of Primary Victim": "Whānau"
            "Parent of Deceased": "Whānau"
            "Parent of Primary Victim": "Whānau"
            "Parent of Witness": "Whānau"
            "Partner of Deceased": "Whānau"
            "Partner of Primary Victim": "Whānau"
            "Perpetrator (Emergency Housing only)": "Other"
            "Present at scene": "Present at scene"
            "Primary Victim (Critical)": "Primary Victim"
            "Primary Victim (Injured)": "Primary Victim (Injured)"
            "Primary Victim (Living)": "Primary Victim"
            "Primary Victim (Missing)": "Primary Victim (Missing)"
            "Primary Victim (Status 1)": "Primary Victim"
            "Property Owner": "Property Owner"
            "Relative of Deceased": "Whānau"
            "Relative of Injured victim": "Whānau"
            "Relative of Primary Victim": "Whānau"
            "Relative of Witness": "Whānau"
            "Sibling in law of Primary Victim": "Whānau"
            "Sibling of Deceased": "Whānau"
            "Sibling of Primary Victim": "Whānau"
            "Son in law of Primary Victim": "Whānau"
            "Son of Deceased": "Whānau"
            "Son of Primary Victim": "Whānau"
            "Spouse of Primary Victim": "Whānau"
            "Step child of Deceased": "Whānau"
            "Step child of Primary Victim": "Whānau"
            "Step parent of Deceased": "Whānau"
            "Step parent of Primary Victim": "Whānau"
            "Step Sibling of Deceased": "Whānau"
            "Step Sibling of Primary Victim": "Whānau"
            "Support Person": "Support Person"
            "Uncle of Primary Victim": "Whānau"
            "Whangai Daughter": "Whānau"
            "Whangai Father": "Whānau"
            "Whangai Mother": "Whānau"
            "Whangai Sibling": "Whānau"
            "Whangai Son": "Whānau"
            "Witness": "Witness"

dataSources:
    # === ENHANCED MULTI-FILE SOURCE DATA ===
    source_cases:
        type: csv
        filePattern: "./fc/simple_case*.csv"
        sortFiles: true
        addFileMetadata: true
        splitFilesIntoJobs: true # Enabled for multi-job processing
        batchSize: 1000

    source_support_events:
        type: csv
        fileList:
            - "./fc/simple_support_events_1.csv"
            - "./fc/simple_support_events_2.csv"
            - "./fc/simple_support_events_3.csv"
        sortFiles: true
        addFileMetadata: true
        splitFilesIntoJobs: true # Enabled for multi-job processing
        batchSize: 2000

    source_victim_assignments:
        type: csv
        fileList:
            - "./fc/simple_victim_assignment_1.csv"
            - "./fc/simple_victim_assignment_2.csv"
            - "./fc/simple_victim_assignment_3.csv"
            - "./fc/simple_victim_assignment_4.csv"
        sortFiles: true
        addFileMetadata: true
        splitFilesIntoJobs: true # Enabled for multi-job processing
        batchSize: 2000

    source_incident_roles:
        type: csv
        filePath: "./fc/simple_incident_role.csv"

    # === TARGET DATA FOR ID MAPPING ===
    migrated_incident_cases:
        type: api
        connection: targetOrg
        object: Case
        fields: ["Id", "Viva_ID__c"]
        whereClause: "Viva_ID__c != null AND RecordType.Name = 'Incident'"
        batchSize: 2000

    migrated_support_event_cases:
        type: api
        connection: targetOrg
        object: Case
        fields: ["Id", "Viva_ID__c"]
        whereClause: "Viva_ID__c != null AND RecordType.Name = 'Support Event'"
        batchSize: 2000

    # === CSV MAPPINGS (created and consumed within this migration) ===
    incident_case_id_map_csv:
        type: csv
        filePath: "./data/incident_case_id_map.csv"

    support_event_case_id_map_csv:
        type: csv
        filePath: "./data/support_event_case_id_map.csv"

flows:
    # === PHASE 1: INCIDENT CASES ===
    migrate_incident_cases:
        name: "S4.1 - Migrate Incident Cases (Multi-File)"
        sources:
            - name: source_cases
              alias: srcCase
            - name: account_id_map_csv # Assuming this is defined in vha_base_config.yml or elsewhere
              alias: acctIdMap
        transform:
            type: join
            primarySource: srcCase
            joins:
                - sourceAlias: acctIdMap
                  joinType: left
                  on:
                      - primaryField: "AccountId"
                        joinField: "Source_Account_Id"
            mappings:
                Viva_ID__c: $srcCase.Id
                RecordTypeId: "$globalVariables.caseRecordTypes.Incident"
                Office__c: $acctIdMap.Target_Account_Id
                HOM1_Received__c: $srcCase.Advice_of_Homicide_Enquiry_Received__c
                Alleged_Offender_Notes__c: $srcCase.Alleged_Offender_Notes__c
                Viva_Record__c: $srcCase.CaseNumber
                Charges_Laid_Causing_Injury_Death__c: $srcCase.Charges_Laid_for_Causing_Death__c
                ClosedDate: "$srcCase.ClosedDate ? new Date(parseInt($srcCase.ClosedDate)).toISOString() : null"
                Cultural_Other_Considerations__c: $srcCase.Cultural__c
                CreatedDate: "$srcCase.CreatedDate ? new Date(parseInt($srcCase.CreatedDate)).toISOString() : null"
                Description: $srcCase.Description
                Risk_Assessment_Notes__c: $srcCase.Family_Harm_Risk_Assessment__c
                Family_Harm__c: $srcCase.Family_Violence__c
                Country__c: $srcCase.Incident_Country__c
                Viva_Modified_Date__c: "$srcCase.LastModifiedDate ? new Date(parseInt($srcCase.LastModifiedDate)).toISOString() : null"
                Incident_Date_Time__c: "$srcCase.Incident_Date_Time__c ? new Date(parseInt($srcCase.Incident_Date_Time__c)).toISOString() : null"
                Location_Comments__c: "$truncateText($srcCase.Location_Comments__c, 255)"
                Police_Event_Number__c: "$truncateText($srcCase.Police_Event_Number__c, 10)"
                Police_File_Number__c: "$truncateText($srcCase.Police_Incident_Number__c, 11)"
                Postal_Code__c: $srcCase.Incident_Postal_Code__c # Not truncated as per original
                Street__c: $srcCase.Incident_Street__c
                Suburb__c: $srcCase.Incident_Suburb__c
                Town_City__c: $srcCase.Incident_Town_City__c
                District__c: "$srcCase.Region__c == 'Bereavement' || $srcCase.Region__c == 'NZVSG' ? 'National Office' : $srcCase.Region__c"
                Safety_Notes__c: $srcCase.Safety_Notes__c
                Sexual_Violence__c: $srcCase.Sexual_Violence__c
                Status: "$((status) => ({'Open': 'Open', 'Closed': 'Closed', 'Declined': 'Closed', 'Parked': 'Parked', 'Referred': 'Closed'})[status] || 'Closed')($srcCase.Status)"
                Gang_Related__c: $srcCase.This_Incident_is_Gang_Related__c
                Type: $srcCase.Type
        targets:
            - name: incident_case_preview_export
              loadStrategy: csv-export
              filePath: "./staging/stage4_incident_cases_preview.csv"
            - name: incident_case_salesforce_load
              object: Case
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
              useMultiJob: true
              maxRecordsPerJob: 50000
              maxJobsParallel: 3
              jobSplitStrategy: file
              preserveFileOrder: true
              jobRetryAttempts: 2
              jobCooldownMs: 1500
              saveResults: failures
              streamResults: true
              progressInterval: 10000
              batchSize: 2000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_1_incident_cases_results.csv"

    create_incident_case_id_mapping:
        name: "S4.2 - Create Incident Case ID Mapping"
        dependencies: ["migrate_incident_cases"]
        sources:
            - name: migrated_incident_cases
        transform:
            type: simple
            mappings:
                Source_Incident_ID: "$Viva_ID__c"
                Target_Case_ID: "$Id"
        targets:
            - name: incident_case_id_map_export
              loadStrategy: csv-export
              filePath: "./data/incident_case_id_map.csv"
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_2_incident_case_mapping_results.csv"

    migrate_referrals:
        name: "S4.3 - Migrate Referrals"
        dependencies: ["create_incident_case_id_mapping"]
        sources:
            - name: source_cases
              alias: origCase
            - name: incident_case_id_map_csv
              alias: caseMap
        transform:
            type: join
            primarySource: origCase
            joins:
                - sourceAlias: caseMap
                  joinType: inner
                  on:
                      - primaryField: "Id"
                        joinField: "Source_Incident_ID"
            mappings:
                Referral_Origin__c: "$origCase.Origin"
                CaseId: "$caseMap.Target_Case_ID"
                Source: "$origCase.Referral_Source__c"
                Incident_Type__c: "$origCase.Type"
                Incident_Date_Time__c: "$origCase.Incident_Date_Time__c ? new Date(parseInt($origCase.Incident_Date_Time__c)).toISOString() : null"
                ReferralType: "$globalVariables.referralType"
                ReferrerName: "$origCase.Referrer_Name__c"
                ReferrerPhone: "$origCase.Referrer_Contact_Number__c"
                Referral_Date_Time__c: "$origCase.Referral_Date_Time__c ? new Date(parseInt($origCase.Referral_Date_Time__c)).toISOString() : null"
                Referral_Police_Station__c: "$origCase.Referral_Police_Station__c"
                Viva_ID__c: "$origCase.Id"
        targets:
            - name: referral_preview_export
              loadStrategy: csv-export
              filePath: "./staging/stage4_referrals_preview.csv"
            - name: referral_salesforce_load
              object: Referral
              connection: targetOrg
              operation: upsert
              externalId: "Viva_ID__c"
              loadStrategy: bulk2
              batchSize: 2000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_3_referrals_results.csv"

    # === PHASE 2: SUPPORT EVENT CASES ===
    migrate_support_event_cases:
        name: "S4.4 - Migrate Support Event Cases (3 Files)"
        dependencies: ["migrate_referrals"]
        sources:
            - name: source_support_events
              alias: se
            - name: account_id_map_csv
              alias: acctIdMap
            - name: incident_case_id_map_csv
              alias: caseIdMap
        transform:
            type: join
            primarySource: se
            joins:
                - sourceAlias: acctIdMap
                  joinType: left
                  on:
                      - primaryField: "Assigned_Office__c"
                        joinField: "Source_Account_Id"
                - sourceAlias: caseIdMap
                  joinType: left
                  on:
                      - primaryField: "Incident__c"
                        joinField: "Source_Incident_ID"
            mappings:
                Viva_ID__c: $se.Id
                Viva_Record__c: $se.Name
                Subject: "$'Support Event: ' + ($se.Name || 'Unnamed Event')"
                Description: >-
                    '$([
                      ($se.Assignment_Details__c ? 'Assignment Details: ' + $se.Assignment_Details__c : ''),
                      ($se.Any_Further_Incident_Information__c ? 'Additional Information: ' + $se.Any_Further_Incident_Information__c : ''),
                      ($se.Street_Address__c ? 'Street Address: ' + $se.Street_Address__c : ''),
                      ($se.SW_Allocation__c ? 'SW Allocation: ' + $se.SW_Allocation__c : '')
                    ].filter(Boolean).join("\\n\\n"))'
                Type: $se.Incident_Type__c
                Priority: "$'Medium'"
                Origin: "$'Support Event Migration'"
                RecordTypeId: "$globalVariables.caseRecordTypes.SupportEvent"
                CreatedDate: "$se.CreatedDate ? new Date(parseInt($se.CreatedDate)).toISOString() : null"
                Viva_Modified_Date__c: "$se.LastModifiedDate ? new Date(parseInt($se.LastModifiedDate)).toISOString() : null"
                AccountId: $acctIdMap.Target_Account_Id
                ParentId: $caseIdMap.Target_Case_ID
                Status: >
                    $((s) => ({
                        'Open': 'Review',
                        'Notes Pending Approval': 'Review',
                        'Closure Pending Approval': 'Review',
                        'Closed': 'Closed'
                    }[s] || null))($se.Assignment_Status__c)
        targets:
            - name: support_event_case_preview_export
              loadStrategy: csv-export
              filePath: "./staging/stage4_support_event_cases_preview.csv"
            - name: support_event_case_salesforce_load
              object: Case
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
              useMultiJob: true
              maxRecordsPerJob: 80000
              maxJobsParallel: 3
              jobSplitStrategy: file
              preserveFileOrder: true
              jobRetryAttempts: 2
              jobCooldownMs: 1000
              saveResults: failures
              streamResults: true
              progressInterval: 5000
              batchSize: 1000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_4_support_event_cases_results.csv"

    create_support_event_case_id_mapping:
        name: "S4.5 - Create Support Event Case ID Mapping"
        dependencies: ["migrate_support_event_cases"]
        sources:
            - name: migrated_support_event_cases
        transform:
            type: simple
            mappings:
                Source_Support_Event_ID: "$Viva_ID__c"
                Target_Case_ID: "$Id"
        targets:
            - name: support_event_case_id_map_export
              loadStrategy: csv-export
              filePath: "./data/support_event_case_id_map.csv"
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_5_support_event_case_mapping_results.csv"

    # === PHASE 3: CASE PARTICIPANTS ===
    migrate_victim_assignments_to_case_participants:
        name: "S4.6 - Migrate Victim Assignments to Case Participants (4 Files)"
        dependencies: ["create_support_event_case_id_mapping"]
        sources:
            - name: source_victim_assignments
              alias: va
            - name: incident_case_id_map_csv
              alias: caseMap
            - name: support_event_case_id_map_csv
              alias: supportEventMap
            - name: account_id_map_csv
              alias: acctMapForOffice
            - name: account_id_map_csv
              alias: acctMapForParticipant
        transform:
            type: join
            primarySource: va
            joins:
                - sourceAlias: caseMap
                  joinType: inner
                  on:
                      - primaryField: "Incident__c"
                        joinField: "Source_Incident_ID"
                - sourceAlias: supportEventMap
                  joinType: left
                  on:
                      - primaryField: "Support_Event__c"
                        joinField: "Source_Support_Event_ID"
                - sourceAlias: acctMapForOffice
                  joinType: left
                  on:
                      - primaryField: "Assigned_Office__c"
                        joinField: "Source_Account_Id"
                - sourceAlias: acctMapForParticipant
                  joinType: inner
                  on:
                      - primaryField: "Victim_Contact__c"
                        joinField: "Source_PersonContactId"
            mappings:
                Viva_ID__c: "$`${$va.Id}`"
                UniqueId__c: "$`${$va.Id}`"
                CaseId: "$va.Incident__c ? $caseMap.Target_Case_ID : null"
                ParticipantId: "$va.Victim_Contact__c ? $acctMapForParticipant.Target_Account_Id : null"
                Role: "$globalVariables.roleMap[$va.Role__c] || null"
                Status: "$globalVariables.defaultStatus"
                Office__c: "$va.Assigned_Office__c ? $acctMapForOffice.Target_Account_Id : null"
                Role_description__c: "$va.Role__c"
                CreatedDate: "$va.CreatedDate ? new Date(parseInt($va.CreatedDate)).toISOString() : null"
                Viva_Modified_Date__c: "$va.LastModifiedDate ? new Date(parseInt($va.LastModifiedDate)).toISOString() : null"
        targets:
            - name: victim_assignment_cp_preview_export
              loadStrategy: csv-export
              filePath: "./staging/stage4_victim_assignment_cp_preview.csv"
            - name: victim_assignment_cp_salesforce_load
              object: CaseParticipant
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
              useMultiJob: true
              maxRecordsPerJob: 60000
              maxJobsParallel: 4
              jobSplitStrategy: file
              preserveFileOrder: true
              jobRetryAttempts: 3
              jobCooldownMs: 2000
              saveResults: all
              streamResults: true
              progressInterval: 5000
              batchSize: 1000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_6_victim_assignment_cp_results.csv"

    migrate_incident_roles_to_case_participants:
        name: "S4.7 - Migrate Incident Roles to Case Participants"
        dependencies: ["migrate_victim_assignments_to_case_participants"]
        sources:
            - name: source_incident_roles
              alias: ir
            - name: incident_case_id_map_csv
              alias: caseMap
            - name: account_id_map_csv
              alias: userMapForStaff
        transform:
            type: join
            primarySource: ir
            joins:
                - sourceAlias: caseMap
                  joinType: inner
                  on:
                      - primaryField: "Incident__c"
                        joinField: "Source_Incident_ID"
                - sourceAlias: userMapForStaff
                  joinType: inner
                  on:
                      - primaryField: "Staff__c"
                        joinField: "Source_PersonContactId"
            mappings:
                Viva_ID__c: "$`${$ir.Id}`"
                UniqueId__c: "$`${$ir.Id}`"
                CaseId: "$ir.Incident__c ? $caseMap.Target_Case_ID : null"
                ParticipantId: "$ir.Staff__c ? $userMapForStaff.Target_Account_Id : null"
                Role: "$globalVariables.roleMap[$ir.Role__c] || null"
                Status: "$globalVariables.defaultStatus"
                CreatedDate: "$ir.CreatedDate? new Date(parseInt($ir.CreatedDate)).toISOString() : null"
                Viva_Modified_Date__c: "$ir.LastModifiedDate? new Date(parseInt($ir.LastModifiedDate)).toISOString() : null"
        targets:
            - name: incident_role_cp_preview_export
              loadStrategy: csv-export
              filePath: "./staging/stage4_incident_role_cp_preview.csv"
            - name: incident_role_cp_salesforce_load
              object: CaseParticipant
              connection: targetOrg
              operation: upsert
              externalId: Viva_ID__c
              loadStrategy: bulk2
              useOptimizedBulk: true
              saveResults: failures
              streamResults: true
              progressInterval: 5000
              batchSize: 1000
        saveJobResults: true
        jobResultsPath: "./staging/job_results/S04_7_incident_role_cp_results.csv"
