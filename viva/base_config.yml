# Base configuration for all migrations
# Contains common connections and shared configurations

# Note: This is a partial configuration that must be extended by a complete migration
migration:
    version: "1.0.0"
    continueOnError: true

# Common connection definitions
connections:
    sourceOrg:
        sfdxAlias: "vicPartial" # Source org alias
        apiVersion: "63.0"
    targetOrg:
        sfdxAlias: "vicFC" # Target org alias
        apiVersion: "63.0"

# Common data sources that can be reused
dataSources:
    # Standard CSV mappings that are generated by various flows
    account_id_map_csv:
        type: csv
        filePath: "./data/account_id_mapping.csv"
        batchSize: 1000 # Optimized for large mapping files

    program_id_map_csv:
        type: csv
        filePath: "./data/program_id_map.csv"

    benefittype_id_map_csv:
        type: csv
        filePath: "./data/benefittype_id_map.csv"

    unitofmeasure_id_map_csv:
        type: csv
        filePath: "./data/unitofmeasure_id_map.csv"

    benefit_id_map_csv:
        type: csv
        filePath: "./data/benefit_id_map.csv"

# Empty flows - must be defined in child configs
flows: {}

# Common target definitions template (for reference in child configs)
# These can be copied and customized in specific migration files
targetTemplates:
    bulkTarget:
        connection: "targetOrg"
        operation: "upsert"
        loadStrategy: "bulk2"
        batchSize: 2000

    restTarget:
        connection: "targetOrg"
        operation: "upsert"
        loadStrategy: "rest"
        batchSize: 200

    csvExportTarget:
        loadStrategy: "csv-export"
