# VHA-specific base configuration
# Extends the general base configuration and adds VHA-specific settings

extends: "./base_config.yml"

migration:
    globalVariables:
        # Common Record Type Mappings (Source ID -> Target ID)
        accountRecordTypeMap:
            "01290000000RehzAAC": "0128s000003sZDZAA2" # Office -> Target Office RT
            "01290000000ResxAAC": "012F9000000esuHIAQ" # Victim -> Target Person Account RT
            "01290000000Ro5gAAC": "0128s000003sZDcAAM" # Counsellor -> Target Counsellor RT
            "01290000000Ro5hAAC": "012F9000000esuHIAQ" # Deceased Victim -> Target Person Account RT
            "01290000000Ro5iAAC": "012F9000000esuHIAQ" # Person Account -> Target Person Account RT
            "01290000000Ro5jAAC": "012F9000000esuHIAQ" # Support Worker -> Target Person Account RT

        # Deceased Victim Source RT ID for special handling
        deceasedVictimSourceRtId: "01290000000Ro5hAAC"

        caseRecordTypes:
            Incident: "0128s000003sZDbAAM"
            SupportEvent: "012F9000000esuNIAQ"

        # Default migration timestamp placeholder
        migrationTimestamp: "2025-06-04T00:00:00.000Z"

            # Record Type to Program Mapping
        applicationRecordTypeToProgram:
            "Travel Assistance - CSHTA": "Homicide Travel Assistance (HHTA)"
            "Non VAS - WC": null
            "Non VAS - WO": null
            "Court Attendance - CSCA": "Homicide Court Attendance (HHCA)"
            "Travel Assistance - CSSCTA": "Homicide Travel Assistance (HHTA)"
            "Grants - CSSCQ": "Serious Crime (SC)"
            "Non VAS - CSG": null
            "Non VAS - CSLS": null
            "Non VAS - CSFS": null
            "Non VAS - CSTA": null
            "Non VAS - CSC": null
            "Non VAS - CSO": null
            "Grants - SVS": "Sexual Violence Supplementary Allowance (SVS)"
            "Counselling - SCC": "Serious Crime Counselling (SCC)"
            "Serious Crime SC": "Serious Crime (SC)"
            "Counselling - WC": "Regulatory Agency Counselling (RAC)"
            "Travel Assistance - WTA": "Regulatory Agency Travel Assistance (RATA)"
            "Court Attendance - DCCA": "Death by Criminal Act Court Attendance (DCCA)"
            "Court Attendance - SCCA": "Serious Crime Court Attendance (SCCA)"
            "Non VAS Generic": null
            "Non VAS Suicide": null
            "Counselling - CSHC": "Homicide Counselling (HC)"
            "Travel Assistance - RATA": "Regulatory Agency Travel Assistance (RATA)"
            "Counselling - RAC": "Regulatory Agency Counselling (RAC)"
            "Civil Defence - CDEM": null
            "Counselling - DCC": "Death by Criminal Act Counselling (DCC)"
            "Counselling - HC": "Homicide Counselling (HC)"
            "Court Attendance - HHCA": "Homicide Court Attendance (HHCA)"
            "Court Attendance - SVCA": "Sexual Violence Court Attendance (SVCA)"
            "Emergency - SCE": "Serious Crime Emergency (SCE)"
            "Grants - DCD": "Death by Criminal ACT Grant (DCD)"
            "Grants - HD": "Homicide Grant (HD)"
            "Grants - SV": "Sexual Violence Grant (SV)"
            "Travel Assistance - DCTA": "Death by Criminal Act Travel Assistance (DCTA)"
            "Travel Assistance - HTA": "Homicide Travel Assistance (HHTA)"
            "Travel Assistance - SCTA": "Serious Crime Travel Assistance (SCTA)"
            "Travel Assistance - SVTA": "Sexual Violence Travel Assistance (SVTA)"
            "Crime Scene - CSE": "Crime Scene Emergency (CSE)"
            "Travel Assistance - CSMOJDATA": "Serious Crime (SC)"
        # Status mappings
        applicationStatusMapping:
            "Draft": "SW Review"
            "Pending Manager Approval": "SW Review"
            "Pending VAS Admin Approval": "Submitted to VAS"
            "Approved": "Completed"
        paymentStatusMapping:
            "Declined/Void": "Cancelled"
            "Draft": "VAS Processed"
            "Over-payment Recovery": "Over-payment Recovery"
            "Paid": "Paid"
            "Processed": "VAS Processed"
            "Refunded": "Refund"
            "Void": "Cancelled"

# VHA-specific common data sources
dataSources:
    # Commonly queried migrated records from target org
    migrated_accounts:
        type: api
        connection: targetOrg
        object: Account
        fields: ["Id", "Viva_ID__c", "Name"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

    migrated_programs:
        type: api
        connection: targetOrg
        object: Program
        fields: ["Id", "Viva_ID__c"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

    migrated_benefits:
        type: api
        connection: targetOrg
        object: Benefit
        fields: ["Id", "Viva_ID__c"]
        whereClause: "Viva_ID__c != null"
        batchSize: 2000

# Common VHA target configurations
commonTargets:
    # Account migration target
    accountTarget:
        object: "Account"
        connection: "targetOrg"
        operation: "upsert"
        externalId: "Viva_ID__c"
        loadStrategy: "bulk2"
        batchSize: 1000

    # Program-related targets
    programTarget:
        object: "Program"
        connection: "targetOrg"
        operation: "upsert"
        externalId: "Viva_ID__c"
        loadStrategy: "bulk2"
        batchSize: 1000

    benefitTypeTarget:
        object: "BenefitType"
        connection: "targetOrg"
        operation: "upsert"
        externalId: "Viva_ID__c"
        loadStrategy: "bulk2"
        batchSize: 2000

    benefitTarget:
        object: "Benefit"
        connection: "targetOrg"
        operation: "upsert"
        externalId: "Viva_ID__c"
        loadStrategy: "bulk2"
        batchSize: 1000
